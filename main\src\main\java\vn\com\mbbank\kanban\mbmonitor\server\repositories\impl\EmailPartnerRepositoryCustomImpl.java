package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.Map;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import vn.com.mbbank.kanban.core.utils.KanbanEntityUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailPartnerEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailPartnerPaginationModel;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.EmailPartnerRepositoryCustom;

/**
 * Implement EmailPartnerRepositoryCustom table EMAIL_PARTNER.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EmailPartnerRepositoryCustomImpl implements EmailPartnerRepositoryCustom {
  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public Page<EmailPartnerEntity> findAll(EmailPartnerPaginationModel request) {
    var query = new PrepareQuery("""
        SELECT emailPartner.ID as id, emailPartner.NAME as name
        FROM EMAIL_PARTNER emailPartner
        LEFT JOIN EMAIL_PARTNER_ADDRESS emailPartnerAddress
            ON emailPartner.ID = emailPartnerAddress.EMAIL_PARTNER_ID
        WHERE 1=1
        """).append(buildQuerySearchLike(request.getSearch()))
        .append(" GROUP BY emailPartner.ID, emailPartner.NAME, emailPartner.CREATED_DATE ");
    String sortColumn =
        KanbanEntityUtils.getColumnName(request.getSortBy(), EmailPartnerEntity.class);
    if (!StringUtils.isNullOrEmpty(sortColumn)) {
      query.append(" ORDER BY emailPartner.").append(sortColumn);
      if (Objects.nonNull(request.getSortOrder())) {
        query.append(" ").append(request.getSortOrder().name());
      }
    }
    return sqlQueryUtil.queryModel()
        .queryPaging(query.getQueryBuilder().toString(), query.getParams(),
            EmailPartnerEntity.class,
            PageRequest.of(request.getPage(), request.getSize()));
  }

  PrepareQuery buildQuerySearchLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    PrepareQuery prepareQuery = new PrepareQuery();
    prepareQuery.append(new PrepareQuery("AND LOWER(emailPartner.NAME) LIKE :search "),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(emailPartnerAddress.ADDRESS) LIKE :search ",
        Map.of("search", search.toLowerCase())), LikeMatcher.CONTAINING);
    return prepareQuery;
  }

}
