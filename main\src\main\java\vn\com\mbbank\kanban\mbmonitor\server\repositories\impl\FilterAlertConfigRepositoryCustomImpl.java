package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import vn.com.mbbank.kanban.core.utils.KanbanEntityUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FilterAlertConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.FilterAlertPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.FilterAlertConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.FilterAlertConfigRepositoryCustom;


/**
 * FilterAlertConfigRepositoryCustomImpl.
 */
@RequiredArgsConstructor
public class FilterAlertConfigRepositoryCustomImpl implements FilterAlertConfigRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public Page<FilterAlertConfigResponse> findAllBySearch(FilterAlertPaginationRequest request) {
    var query = new PrepareQuery("""
        SELECT filterAlertConfig.ID AS id,
          filterAlertConfig.NAME AS name,
          filterAlertConfig.DESCRIPTION AS description,
          filterAlertConfig.ACTIVE AS active,
          filterAlertConfig.RULE_GROUP AS ruleGroupColumn
          FROM FILTER_ALERT_CONFIG filterAlertConfig
          WHERE 1=1
        """).append(buildQuerySearchLike(request.getSearch()));
    String sortColumn = KanbanEntityUtils.getColumnName(request.getSortBy(), FilterAlertConfigEntity.class);
    if (!StringUtils.isNullOrEmpty(sortColumn)) {
      query.append(" ORDER BY filterAlertConfig.").append(sortColumn);
      if (Objects.nonNull(request.getSortOrder())) {
        query.append(" ").append(request.getSortOrder().name()).append(", filterAlertConfig.ID");
      }
    }
    return sqlQueryUtil.queryModel().queryPaging(query.getQuery(), query.getParams(), FilterAlertConfigResponse.class,
        PageRequest.of(request.getPage(), request.getSize()));
  }

  PrepareQuery buildQuerySearchLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    PrepareQuery prepareQuery = new PrepareQuery();
    prepareQuery.append(new PrepareQuery(" AND LOWER(filterAlertConfig.NAME) LIKE :search "),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" AND LOWER(filterAlertConfig.RULE_GROUP) LIKE :search "),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(filterAlertConfig.DESCRIPTION) LIKE :search ",
        Map.of("search", search.toLowerCase())), LikeMatcher.CONTAINING);
    return prepareQuery;
  }

}
