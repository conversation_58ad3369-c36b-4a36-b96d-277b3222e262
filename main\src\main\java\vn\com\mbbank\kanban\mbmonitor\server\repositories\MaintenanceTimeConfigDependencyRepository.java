package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigDependencyEntity;

/**
 * Repository table AlertGroupConfigDependencyRepository.
 */
@Repository
public interface MaintenanceTimeConfigDependencyRepository
    extends JpaCommonRepository<MaintenanceTimeConfigDependencyEntity, Long> {

  /**
   * find all AlertGroupConfigDependencyEntity.
   *
   * @param alertGroupConfigId alertGroupConfigId
   * @return list of AlertGroupConfigDependencyEntity
   */
  List<MaintenanceTimeConfigDependencyEntity> findAllByMaintenanceTimeConfigId(Long alertGroupConfigId);

  /**
   * delete all AlertGroupConfigDependencyEntity.
   *
   * @param alertGroupConfigId alertGroupConfigId
   * @return number of AlertGroupConfigConditionEntity deleted
   */
  long deleteAllByMaintenanceTimeConfigId(Long alertGroupConfigId);
}
