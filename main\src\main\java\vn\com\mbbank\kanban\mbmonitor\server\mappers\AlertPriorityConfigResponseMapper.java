package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AlertPriorityConfigResponse;

/**
 * Mapper logic AlertPriorityConfigEntity.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AlertPriorityConfigResponseMapper extends
    KanbanBaseMapper<AlertPriorityConfigResponse, AlertPriorityConfigEntity> {

  AlertPriorityConfigResponseMapper INSTANCE =
      Mappers.getMapper(AlertPriorityConfigResponseMapper.class);
}
