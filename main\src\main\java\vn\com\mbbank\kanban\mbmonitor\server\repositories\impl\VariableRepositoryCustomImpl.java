package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.VariableResponse;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.VariableRepositoryCustom;

/**
 * variableRepositoryCustomImpl.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class VariableRepositoryCustomImpl implements VariableRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public Page<VariableResponse> findAll(PaginationRequestDTO paginationRequest) {
    var query = new PrepareQuery("""
        SELECT variable.ID AS "id",
        variable.NAME AS "name",
        variable.DESCRIPTION AS "description",
        variable.VALUE AS "value",
        variable.HIDDEN AS "hidden",
        variable.TYPE AS "type",
        variable.EXECUTION_ID AS "executionId",
        execution.NAME AS "executionName",
        variable.DATA_TYPE AS "dataType",
        variable.JSON_PATH AS "jsonPath",
        variable.EXPIRATION_TIME AS "expirationTime",
        variable.ENABLE_EXPIRATION AS "enableExpiration"
        FROM VARIABLE variable LEFT JOIN EXECUTION execution ON variable.EXECUTION_ID  = execution.ID
        WHERE 1=1
        """
    ).append(buildQuerySearchLike(paginationRequest.getSearch()))
        .append(buildOrderQuery(paginationRequest));
    Pageable pageable = PageRequest.of(paginationRequest.getPage(),
        paginationRequest.getSize());
    return sqlQueryUtil.queryModel()
        .queryPaging(query.getQueryBuilder().toString(), query.getParams(),
          VariableResponse.class, pageable);
  }
  
  @Override
  public List<String> findExistsVariableNameInExecution(String executionId, Set<String> names) {
    var query = new PrepareQuery("""
          SELECT p.NAME
             FROM EXECUTION_PARAM p
             WHERE p.EXECUTION_ID = :executionId
               AND LOWER(p.NAME) IN (:names)
             FETCH FIRST 1 ROWS ONLY
         """,
        Map.of("executionId", executionId, "names", names)
    );
    
    return sqlQueryUtil.queryModel().queryForList(query.getQuery(), query.getParams(), String.class);
  }
  
  @Override
  public List<String> getReferencedDynamicExecutionIds(String executionId) {
    var query = new PrepareQuery("""
          SELECT DISTINCT v.EXECUTION_ID
          FROM VARIABLE v
          JOIN EXECUTION_PARAM p ON v.NAME = p.NAME
          WHERE p.EXECUTION_ID = :executionId
            AND v.TYPE = 'DYNAMIC_VALUE'
        """, "executionId", executionId);
    
    return sqlQueryUtil.queryModel()
      .queryForList(query.getQuery(), query.getParams(), String.class);
  }
  
  PrepareQuery buildQuerySearchLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    return new PrepareQuery("AND (")
        .append(new PrepareQuery("LOWER(variable.NAME) LIKE :search"),
            LikeMatcher.CONTAINING)
        .append(new PrepareQuery(" OR LOWER(variable.TYPE) LIKE :search"),
            LikeMatcher.CONTAINING)
        .append(new PrepareQuery(" OR LOWER(execution.NAME) LIKE :search"),
            LikeMatcher.CONTAINING)
        .append(new PrepareQuery(" OR LOWER(variable.DESCRIPTION) LIKE :search",
                Map.of("search", search.toLowerCase())),
            LikeMatcher.CONTAINING)
        .append(")");
  }

  PrepareQuery buildOrderQuery(PaginationRequestDTO paginationRequest) {
    var sortBy = paginationRequest.getSortBy();
    PrepareQuery query;
    if (StringUtils.isBlank(sortBy)) {
      query = new PrepareQuery(" ORDER BY variable.CREATED_DATE ");
    } else {
      query = switch (sortBy) {
        case "name" -> new PrepareQuery(" ORDER BY variable.NAME ");
        case "description" -> new PrepareQuery(" ORDER BY variable.DESCRIPTION ");
        case "executionName" -> new PrepareQuery(" ORDER BY execution.NAME ");
        case "type" -> new PrepareQuery(" ORDER BY variable.TYPE ");
        default -> new PrepareQuery(" ORDER BY variable.CREATED_DATE ");
      };
    }
    return query.append(
        Objects.isNull(paginationRequest.getSortOrder()) ? null : paginationRequest.getSortOrder().name());
  }
}
