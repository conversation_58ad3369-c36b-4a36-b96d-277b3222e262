package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.SysLogCursor;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.SysLogRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.SysLogResponse;

/**
 * Custom Repo table SysLog.
 */
public interface SysLogRepositoryCustom {

  /**
   * Find all syslog by request.
   *
   * @param request SysLogRequest
   * @return CursorPageResponse
   */
  CursorPageResponse<SysLogResponse, SysLogCursor> findAll(SysLogRequest request);
}
