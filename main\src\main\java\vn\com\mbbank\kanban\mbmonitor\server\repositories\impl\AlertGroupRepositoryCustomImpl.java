package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.enums.SortType;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.AlertAmountModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertGroupSearchRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AlertGroupResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.AlertGroupRepositoryCustom;

/**
 * AlertGroupRepositoryCustomImpl.
 */
@RequiredArgsConstructor
public class AlertGroupRepositoryCustomImpl implements AlertGroupRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public Page<AlertGroupResponse> findAll(AlertGroupSearchRequest searchRequest,
                                          PaginationRequestDTO paginationRequest) {
    var query = new PrepareQuery(
        """
            SELECT alert.ID,
                   alert.CONTENT,
                   alert.RECIPIENT,
                   alert.STATUS,
                   alert.CREATED_DATE AS createdDate,
                   alert.ALERT_GROUP_ID AS alertGroupId,
                   alert.ALERT_PRIORITY_CONFIG_ID AS priorityConfigId,
                   alertPriorityConfig.NAME as priorityName,
                   alertPriorityConfig.COLOR as priorityColor,
                   alert.SERVICE_ID AS serviceId,
                   service.NAME AS serviceName,
                   alert.APPLICATION_ID AS applicationId,
                   application.NAME as applicationName
            FROM ALERT_GROUP alertGroup
            JOIN ALERT alert ON alertGroup.PRIMARY_ALERT_ID = alert.ID
            LEFT JOIN SERVICE service ON alert.SERVICE_ID = service.ID
            LEFT JOIN APPLICATION application ON alert.APPLICATION_ID = application.ID
            LEFT JOIN ALERT_PRIORITY_CONFIG alertPriorityConfig
              ON alertPriorityConfig.ID = alert.ALERT_PRIORITY_CONFIG_ID
            WHERE 1=1 AND alertGroup.STATUS = 'NEW'
            """
    )
        .append(buildQueryRecipientLike(searchRequest.getRecipient()), LikeMatcher.CONTAINING)
        .append(buildQueryPriorityConfigIdIn(searchRequest.getAlertPriorityConfigIds()))
        .append(buildQueryServiceIdIn(searchRequest.getServiceIds()))
        .append(buildQueryApplicationIdIn(searchRequest.getApplicationIds()))
        .append(buildQueryContentLike(searchRequest.getContent()), LikeMatcher.CONTAINING)
        .append(buildQuerySortBy(paginationRequest.getSortBy(), paginationRequest.getSortOrder()))
        .append(" OFFSET " + paginationRequest.getPage() * paginationRequest.getSize() + " ROWS ")
        .append(" FETCH FIRST " + (paginationRequest.getSize() + 1) + " ROWS ONLY ");

    // mỗi page sẽ lấy dư thêm 1 phần tử đé xem có page tiếp theo không
    Pageable pageable = PageRequest.of(paginationRequest.getPage(), paginationRequest.getSize());
    var res = sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), AlertGroupResponse.class);
    var result = res.size() > paginationRequest.getSize() ? res.subList(0, paginationRequest.getSize()) : res;
    return new PageImpl<>(result, pageable,
        res.size() + ((long) paginationRequest.getPage() * paginationRequest.getSize()));
  }

  @Override
  public List<AlertAmountModel> getAlertAmountModel(List<Long> alertGroupIds) {
    var alertAmountQuery = new PrepareQuery("""
        SELECT COUNT(*)      AS alertAmount,
               alertGroup.ID AS alertGroupId
        FROM ALERT_GROUP alertGroup
                 JOIN ALERT alert ON alertGroup.ID = alert.ALERT_GROUP_ID
        WHERE alertGroup.ALERT_GROUP_CONFIG_ID <> 0
            """)
        .append("AND alertGroup.ID IN (:alertGroupIds) ", "alertGroupIds", alertGroupIds)
        .append("GROUP BY alertGroup.ID");
    return sqlQueryUtil.queryModel()
        .queryForList(alertAmountQuery.getQuery(), alertAmountQuery.getParams(),
            AlertAmountModel.class);
  }

  @Override
  public List<String> findAlertRecipientByAlertGroupStatus(
      AlertGroupStatusEnum alertGroupStatus) {
    var query = new PrepareQuery("""
            SELECT DISTINCT alert.RECIPIENT AS recipient
            FROM ALERT alert
            JOIN ALERT_GROUP alertGroup
                on alertGroup.PRIMARY_ALERT_ID = alert.ID
            WHERE alert.STATUS = :status
            AND alert.RECIPIENT IS NOT NULL
        """);
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), Map.of("status", alertGroupStatus.name()), String.class);
  }

  @Override
  public List<AlertGroupEntity> findAllByServiceIdAndApplicationIdAndCreateTime(String serviceId,
                                                                                String applicationId,
                                                                                int offsetAgo) {
    var query = new PrepareQuery("""
        SELECT *
        FROM ALERT_GROUP alertGroup
        WHERE 1=1
        """)
        .append(buildServiceIdsEqual(serviceId))
        .append(buildApplicationIdsEqual(applicationId))
        .append(buildCreateTimeGreaterThan(offsetAgo));

    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), AlertGroupEntity.class);
  }

  private PrepareQuery buildJoinQuery(String sortBy) {
    if (StringUtils.isBlank(sortBy)) {
      return null;
    }
    return switch (sortBy) {
      case "serviceName" -> new PrepareQuery(" JOIN SERVICE service ON alert.SERVICE_ID = service.ID ");
      case "applicationName" -> new PrepareQuery(
          " JOIN APPLICATION application ON alert.APPLICATION_ID = application.ID ");
      default -> null;
    };
  }

  private PrepareQuery buildServiceIdsEqual(String serviceIds) {
    if (StringUtils.isBlank(serviceIds)) {
      return null;
    }
    return new PrepareQuery("AND alertGroup.SERVICE_IDS = :serviceIds ",
        Map.of("serviceIds", serviceIds));
  }

  private PrepareQuery buildApplicationIdsEqual(String applicationIds) {
    if (StringUtils.isBlank(applicationIds)) {
      return null;
    }
    return new PrepareQuery("AND alertGroup.APPLICATION_IDS = :applicationIds ",
        Map.of("applicationIds", applicationIds));
  }

  private PrepareQuery buildCreateTimeGreaterThan(int offsetAgo) {
    var past = DateUtils.addSeconds(new Date(), -offsetAgo);
    return new PrepareQuery("AND alertGroup.CREATED_DATE >= :createdDate",
        Map.of("createdDate", past));
  }

  PrepareQuery buildQueryContentLike(String content) {
    if (StringUtils.isBlank(content)) {
      return null;
    }
    return new PrepareQuery(" AND LOWER(alert.CONTENT) LIKE :content",
        "content", content.toLowerCase());
  }

  PrepareQuery buildQueryPriorityConfigIdIn(List<Long> alertPriorityConfigIds) {
    if (CollectionUtils.isEmpty(alertPriorityConfigIds)) {
      return null;
    }
    return new PrepareQuery(" AND alert.ALERT_PRIORITY_CONFIG_ID IN (:alertPriorityConfigIds)",
        "alertPriorityConfigIds",
        alertPriorityConfigIds);
  }

  PrepareQuery buildQueryServiceIdIn(List<String> serviceIds) {
    if (CollectionUtils.isEmpty(serviceIds)) {
      return null;
    }
    return new PrepareQuery(" AND alert.SERVICE_ID IN (:serviceIds)", "serviceIds", serviceIds);
  }

  PrepareQuery buildQueryApplicationIdIn(List<String> applicationIds) {
    if (CollectionUtils.isEmpty(applicationIds)) {
      return null;
    }
    return new PrepareQuery(" AND alert.APPLICATION_ID IN (:applicationIds)", "applicationIds",
        applicationIds);
  }

  PrepareQuery buildQueryRecipientLike(String recipient) {
    if (StringUtils.isBlank(recipient)) {
      return null;
    }
    return new PrepareQuery(" AND LOWER(alert.RECIPIENT) LIKE :recipient", "recipient", recipient.trim().toLowerCase());
  }

  PrepareQuery buildQuerySortBy(String sortBy, SortType sortType) {
    var query = new PrepareQuery(" ORDER BY ");
    if (StringUtils.isBlank(sortBy)) {
      return query.append("alert.CREATED_DATE ").append(String.valueOf(sortType));
    }
    var orderQuery = new PrepareQuery(" ").append(String.valueOf(sortType)).append(", alert.ID");
    return switch (sortBy) {
      case "recipient", "status" -> query.append("alert.").append(sortBy.toUpperCase()).append(orderQuery);
      case "serviceName" -> query.append("service.NAME").append(orderQuery);
      case "applicationName" -> query.append("application.NAME").append(orderQuery);
      default -> query.append("alert.CREATED_DATE ").append(String.valueOf(sortType));
    };
  }
}
