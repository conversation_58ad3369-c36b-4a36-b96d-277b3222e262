package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.logging.log4j.util.Strings;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.RawValueConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.AlertPriorityConfigRepositoryCustom;

/**
 * Implement AlertPriorityConfigRepositoryCustom.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AlertPriorityConfigRepositoryCustomImpl
    implements AlertPriorityConfigRepositoryCustom {

  SqlQueryUtil sqlQueryUtil;

  @Override
  public List<AlertPriorityConfigEntity> findAllMatchPriorityConfig(String rawPriority,
                                                                    String rawAlertContent) {
    var query = new PrepareQuery(
        """
            SELECT alertPriorityConfig.ID,
                   alertPriorityConfig.NAME,
                   alertPriorityConfig.POSITION,
                   alertPriorityConfig.COLOR,
                   alertPriorityConfig.DELETED
            FROM ALERT_PRIORITY_CONFIG alertPriorityConfig
                   LEFT JOIN PRIORITY_RAW_VALUE_CONFIG rawValueConfig
                   ON alertPriorityConfig.ID = rawValueConfig.ALERT_PRIORITY_CONFIG_ID
            WHERE alertPriorityConfig.DELETED = 0
            """
    ).append(buildRawPriorityAndRawAlertContentAndGroup(rawPriority, rawAlertContent)).append(
        """
            GROUP BY alertPriorityConfig.ID,
                      alertPriorityConfig.NAME,
                      alertPriorityConfig.POSITION,
                      alertPriorityConfig.COLOR,
                      alertPriorityConfig.DELETED
            """);
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), AlertPriorityConfigEntity.class);
  }

  @Override
  public List<AlertPriorityConfigEntity> findAllByAlertGroupStatus(
      AlertGroupStatusEnum alertGroupStatus) {
    var query = new PrepareQuery(
        """
            SELECT alertPriorityConfig.ID,
                   alertPriorityConfig.NAME,
                   alertPriorityConfig.POSITION,
                   alertPriorityConfig.COLOR,
                   alertPriorityConfig.DELETED
            FROM ALERT_PRIORITY_CONFIG alertPriorityConfig
            LEFT JOIN ALERT alert
                    ON alert.ALERT_PRIORITY_CONFIG_ID = alertPriorityConfig.ID
            LEFT JOIN ALERT_GROUP alertGroup
                    ON alertGroup.PRIMARY_ALERT_ID = alert.ID
            WHERE 1 = 1
            """)
        .append(buildAlertGroupStatusEq(alertGroupStatus))
        .append(
            """
                GROUP BY alertPriorityConfig.ID,
                       alertPriorityConfig.NAME,
                       alertPriorityConfig.POSITION,
                       alertPriorityConfig.COLOR,
                       alertPriorityConfig.DELETED
                """);
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), AlertPriorityConfigEntity.class);
  }

  @Override
  public Integer findRealPositionById(Long id) {
    var query = new PrepareQuery(
        """
            SELECT POSITION
              FROM (SELECT ROW_NUMBER() OVER (ORDER BY alertPriorityConfig.POSITION) AS POSITION, ID
                    FROM ALERT_PRIORITY_CONFIG alertPriorityConfig
                    WHERE alertPriorityConfig.DELETED = 0)
              WHERE ID = :id
            """, "id", id);
    return sqlQueryUtil.queryModel()
        .queryForObject(query.getQuery(), query.getParams(), Integer.class);
  }

  PrepareQuery buildRawPriorityAndRawAlertContentAndGroup(String rawPriority,
                                                          String rawAlertContent) {
    var isRawPriorityBlank = Strings.isBlank(rawPriority);
    var isRawAlertContentBlank = Strings.isBlank(rawAlertContent);
    if (isRawPriorityBlank && isRawAlertContentBlank) {
      return null;
    }
    return new PrepareQuery(" AND (")
        .append(buildRawPriorityEq(rawPriority))
        .append(!isRawAlertContentBlank && !isRawPriorityBlank ? " OR " : null)
        .append(buildRawAlertContentLike(rawAlertContent))
        .append(")");
  }

  PrepareQuery buildAlertGroupStatusEq(AlertGroupStatusEnum alertGroupStatus) {
    if (Objects.isNull(alertGroupStatus)) {
      return null;
    }
    return new PrepareQuery(" AND alertGroup.STATUS = :alertGroupStatus ",
        Map.of("alertGroupStatus", alertGroupStatus.name()));
  }

  PrepareQuery buildRawPriorityEq(String rawPriority) {
    if (Strings.isBlank(rawPriority)) {
      return null;
    }
    return new PrepareQuery(
        "(rawValueConfig.VALUE = :priority AND rawValueConfig.TYPE = :rawValuePriorityType)",
        Map.of("priority", rawPriority, "rawValuePriorityType",
            RawValueConfigTypeEnum.PRIORITY.name()));
  }

  PrepareQuery buildRawAlertContentLike(String rawAlertContent) {
    if (Strings.isBlank(rawAlertContent)) {
      return null;
    }
    return new PrepareQuery(
        " (:rawAlertContent LIKE ('%' || rawValueConfig.VALUE || '%') AND rawValueConfig.TYPE = :rawValueType)",
        Map.of("rawAlertContent", rawAlertContent, "rawValueType",
            RawValueConfigTypeEnum.ALERT_CONTENT.name()));
  }
}
