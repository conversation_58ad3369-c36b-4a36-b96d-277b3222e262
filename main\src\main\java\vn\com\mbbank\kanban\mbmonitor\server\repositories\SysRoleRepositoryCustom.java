package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SysRoleWithPermissionsModel;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 9/23/2024
 */
public interface SysRoleRepositoryCustom {
  /**
   * Find all role by list userId.
   *
   * @param userIds userIds
   * @return list SysRoleModel
   */
  List<SysRoleWithPermissionsModel> findAllByUserIdIn(List<Long> userIds);

}
