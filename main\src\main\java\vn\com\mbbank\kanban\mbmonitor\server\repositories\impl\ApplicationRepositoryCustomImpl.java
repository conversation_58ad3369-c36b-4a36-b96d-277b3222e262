package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import vn.com.mbbank.kanban.core.utils.KanbanEntityUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.ApplicationWithPriorityModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ApplicationPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ApplicationResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.ApplicationRepositoryCustom;

/**
 * Implement ApplicationRepositoryCustom table APPLICATION.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ApplicationRepositoryCustomImpl implements ApplicationRepositoryCustom {
  SqlQueryUtil sqlQueryUtil;

  @Override
  public List<ApplicationWithPriorityModel> findApplicationWithPriorityByAlertGroupStatusAndServiceId(
      AlertGroupStatusEnum alertGroupStatus, String serviceId) {
    PrepareQuery query = new PrepareQuery("""
        SELECT application.ID                   AS id,
               application.NAME                 AS name,
               application.SERVICE_ID           AS serviceId,
               alert.ALERT_PRIORITY_CONFIG_ID   AS alertPriorityConfigId,
               COUNT(alertGroup.PRIMARY_ALERT_ID) AS alertAmount
        FROM ALERT_GROUP alertGroup
        JOIN ALERT alert
             on alertGroup.PRIMARY_ALERT_ID = alert.ID
        LEFT JOIN APPLICATION application
             ON alert.APPLICATION_ID = application.ID
        WHERE 1 = 1
        """)
        .append(buildQueryStatusEq(alertGroupStatus))
        .append(buildQueryServiceIdEq(serviceId))
        .append("""
            GROUP BY application.ID, application.NAME, application.SERVICE_ID, alert.ALERT_PRIORITY_CONFIG_ID
            ORDER BY application.NAME
            """);
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQueryBuilder().toString(), query.getParams(),
            ApplicationWithPriorityModel.class);
  }

  @Override
  public ApplicationResponse findApplicationById(String id) {
    var query = new PrepareQuery("""
        SELECT application.ID AS id, application.NAME AS name, application.DESCRIPTION as description,
        application.SERVICE_ID AS serviceId, service.NAME AS serviceName
        FROM APPLICATION application
        INNER JOIN SERVICE service ON application.SERVICE_ID=service.ID
        """
    ).append(" WHERE application.ID= :id", "id", id);
    return sqlQueryUtil.queryModel()
        .queryForObject(query.getQueryBuilder().toString(), query.getParams(),
            ApplicationResponse.class);
  }

  @Override
  public List<ApplicationResponse> findAllByIdIn(List<String> ids) {
    var query = new PrepareQuery("""
        SELECT application.ID AS id, application.NAME AS name, application.DESCRIPTION as description,
        application.SERVICE_ID AS serviceId, service.NAME AS serviceName
        FROM APPLICATION application
        INNER JOIN SERVICE service ON application.SERVICE_ID=service.ID
        """
    ).append(" WHERE application.ID in (:ids)", "ids", ids);
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQueryBuilder().toString(), query.getParams(),
            ApplicationResponse.class);
  }

  private PrepareQuery buildQueryStatusEq(AlertGroupStatusEnum alertGroupStatus) {
    if (Objects.isNull(alertGroupStatus)) {
      return null;
    }
    return new PrepareQuery(" AND alertGroup.STATUS = :status ", "status", alertGroupStatus.name());
  }

  private PrepareQuery buildQueryServiceIdEq(String serviceId) {
    if (StringUtils.isBlank(serviceId)) {
      return null;
    }
    return new PrepareQuery(" AND application.SERVICE_ID = :serviceId ", "serviceId", serviceId);
  }

  @Override
  public Page<ApplicationResponse> findAll(
      ApplicationPaginationRequest applicationPaginationRequest) {
    var query = new PrepareQuery("""
        SELECT application.ID AS id, application.NAME AS name, application.SERVICE_ID AS serviceId,
        application.DESCRIPTION as description, service.NAME AS serviceName,
        application.CREATED_DATE AS createdDate
        FROM APPLICATION application
        INNER JOIN SERVICE service ON application.SERVICE_ID=service.ID
        WHERE 1=1
        """
    ).append(buildQueryServiceIdIn(applicationPaginationRequest.getServiceIds()))
        .append(buildQueryNameLike(applicationPaginationRequest.getName()), LikeMatcher.CONTAINING)
        .append(buildQuerySearchLike(applicationPaginationRequest.getSearch()))
        .append(buildQueryDeletedEq(
            Boolean.TRUE.equals(applicationPaginationRequest.getWithDeleted())));

    String sortColumn = KanbanEntityUtils.getColumnName(applicationPaginationRequest.getSortBy(),
        ApplicationEntity.class);
    if (!StringUtils.isNullOrEmpty(sortColumn)) {
      query.append(" ORDER BY APPLICATION.").append(sortColumn);
      if (Objects.nonNull(applicationPaginationRequest.getSortOrder())) {
        query.append(" ").append(applicationPaginationRequest.getSortOrder().name());
      }
    }
    Pageable pageable = PageRequest.of(applicationPaginationRequest.getPage(),
        applicationPaginationRequest.getSize());
    return sqlQueryUtil.queryModel()
        .queryPaging(query.getQueryBuilder().toString(), query.getParams(),
            ApplicationResponse.class, pageable);
  }

  PrepareQuery buildQueryDeletedEq(boolean withDeleted) {
    return withDeleted ? new PrepareQuery() : new PrepareQuery(" AND application.DELETED = 0");
  }

  PrepareQuery buildQueryServiceIdIn(List<String> serviceIds) {
    if (CollectionUtils.isEmpty(serviceIds)) {
      return null;
    }
    return new PrepareQuery(" AND application.SERVICE_ID IN (:serviceIds)", "serviceIds",
        serviceIds);
  }

  PrepareQuery buildQueryNameLike(String name) {
    if (StringUtils.isBlank(name)) {
      return null;
    }
    return new PrepareQuery(" AND LOWER(application.NAME) LIKE :name",
        Map.of("name", name.toLowerCase()));
  }

  PrepareQuery buildQuerySearchLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    PrepareQuery prepareQuery = new PrepareQuery();
    prepareQuery.append(new PrepareQuery("AND (LOWER(application.NAME) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(application.DESCRIPTION) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(application.ID) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(application.SERVICE_ID) LIKE :search",
        Map.of("search", search.toLowerCase())), LikeMatcher.CONTAINING);
    prepareQuery.append(")");
    return prepareQuery;
  }

}
