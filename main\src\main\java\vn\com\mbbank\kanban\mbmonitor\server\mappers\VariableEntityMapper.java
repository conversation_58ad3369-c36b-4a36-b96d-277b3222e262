package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import java.util.List;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.VariableRequest;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;

/**
 * VariableEntityMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VariableEntityMapper
    extends KanbanBaseMapper<VariableEntity, VariableRequest> {
  VariableEntityMapper INSTANCE = Mappers.getMapper(VariableEntityMapper.class);
  
  /**
   * Convert request details to a list of VariableEntity with fields from both detail and request.
   *
   * @param request the variable request containing the input data
   * @return a list of mapped {@link VariableEntity} objects
   */
  default List<VariableEntity> mapToList(VariableRequest request) {
    return request.getVariableDetails().stream().map(detail -> {
      VariableEntity entity = new VariableEntity();
      
      // Set fields from detail
      entity.setName(detail.getName());
      entity.setJsonPath(detail.getJsonPath());
      entity.setHidden(detail.isHidden());
      
      // Set shared fields from request
      entity.setId(null);
      entity.setDescription(request.getDescription());
      entity.setType(request.getType());
      entity.setDataType(request.getDataType());
      entity.setValue("");
      entity.setExecutionId(request.getExecutionId());
      entity.setEnableExpiration(request.isEnableExpiration());
      entity.setExpirationTime(request.getExpirationTime());
      
      return entity;
    }).collect(Collectors.toList());
  }
}
