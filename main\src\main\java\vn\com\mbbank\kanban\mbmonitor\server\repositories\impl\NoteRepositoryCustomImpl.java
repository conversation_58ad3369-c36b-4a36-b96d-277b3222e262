package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.time.DateUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.AlertGroupNoteAmountModel;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.NoteRepositoryCustom;


/**
 * Implement NoteRepositoryCustom table NOTE.
 */
@RequiredArgsConstructor
public class NoteRepositoryCustomImpl implements NoteRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public List<AlertGroupNoteAmountModel> countNoteCreateInSecondAgoByAlertGroupIdIn(List<Long> alertGroupIds,
                                                                                    int secondAgo) {
    var past = DateUtils.addSeconds(new Date(), -secondAgo);
    var query = """
        SELECT note.ALERT_GROUP_ID as alertGroupId, COUNT(note.ID) AS noteAmount
        FROM NOTE note
        WHERE note.CREATED_DATE >= :past AND note.ALERT_GROUP_ID in (:alertGroupIds)
        GROUP BY note.ALERT_GROUP_ID
        """;
    return sqlQueryUtil.queryModel()
        .queryForList(query, Map.of("past", past, "alertGroupIds", alertGroupIds), AlertGroupNoteAmountModel.class);
  }
}
