package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionGroupResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionGroupEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.ExecutionGroupRepositoryCustom;

/**
 * ExecutionGroupRepositoryCustomImpl.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ExecutionGroupRepositoryCustomImpl implements ExecutionGroupRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public Page<ExecutionGroupEntity> findAllByIdsAndExecutionIds(PaginationRequestDTO paginationRequest,
                                                                List<String> ids,
                                                                List<String> executionIds) {
    var query = new PrepareQuery("""
        SELECT *
        FROM EXECUTION_GROUP executionGroup
        WHERE 1=1
        """
    )
        .append(buildQueryIdIn(ids))
        .append(buildQueryNameLikeOrAtLeast1ExecutionNameLike(paginationRequest.getSearch(), ids, executionIds))
        .append(buildOrderQuery(paginationRequest));
    Pageable pageable = PageRequest.of(paginationRequest.getPage(),
        paginationRequest.getSize());
    return sqlQueryUtil.queryModel()
        .queryPaging(query.getQuery(), query.getParams(),
            ExecutionGroupEntity.class, pageable);
  }

  @Override
  public Page<ExecutionGroupEntity> findAll(PaginationRequestDTO paginationRequest) {
    var query = new PrepareQuery("""
        SELECT *
        FROM EXECUTION_GROUP executionGroup
        WHERE 1=1
        """
    ).append(buildQueryAndAndDescriptionLike(paginationRequest.getSearch()))
        .append(buildOrderQuery(paginationRequest));
    Pageable pageable = PageRequest.of(paginationRequest.getPage(),
        paginationRequest.getSize());
    return sqlQueryUtil.queryModel()
        .queryPaging(query.getQuery(), query.getParams(),
            ExecutionGroupEntity.class, pageable);
  }

  @Override
  public List<ExecutionGroupResponse> findAllWithExecutionAmount() {
    String query = """
             SELECT executionGroup.ID,
                    executionGroup.NAME,
                    executionGroup.DESCRIPTION,
                    COUNT(execution.ID) AS       executionAmount
             FROM EXECUTION_GROUP executionGroup
                      LEFT JOIN EXECUTION execution
                      ON executionGroup.ID = execution.EXECUTION_GROUP_ID
             GROUP BY executionGroup.ID, executionGroup.NAME, executionGroup.DESCRIPTION
        """;
    return sqlQueryUtil.queryModel()
        .queryForList(query, ExecutionGroupResponse.class);
  }

  PrepareQuery buildQueryAndAndDescriptionLike(String search) {
    if (KanbanCommonUtil.isEmpty(search)) {
      return null;
    }
    return new PrepareQuery("AND (")
        .append(new PrepareQuery("LOWER(executionGroup.NAME) LIKE :search"),
            LikeMatcher.CONTAINING)
        .append(new PrepareQuery(" OR LOWER(executionGroup.DESCRIPTION) LIKE :search",
                Map.of("search", search.toLowerCase())),
            LikeMatcher.CONTAINING)
        .append(")");
  }

  PrepareQuery buildQueryNameLikeOrAtLeast1ExecutionNameLike(String search, List<String> ids,
                                                             List<String> executionIds) {
    if (KanbanCommonUtil.isEmpty(search)) {
      return null;
    }
    var searchValue = search.toLowerCase();
    return new PrepareQuery("""
        AND (EXISTS (
            SELECT 1
            FROM EXECUTION execution
            WHERE execution.EXECUTION_GROUP_ID = executionGroup.ID
        """)
        .append(buildOrQueryIdsAndExecutionIds(ids, executionIds))
        .append(
            new PrepareQuery(" AND LOWER(execution.NAME) LIKE :search ", "search", searchValue), LikeMatcher.CONTAINING)
        .append(new PrepareQuery(") OR LOWER(executionGroup.NAME) LIKE :search "), LikeMatcher.CONTAINING).append(")");
  }

  PrepareQuery buildOrQueryIdsAndExecutionIds(List<String> ids, List<String> executionIds) {
    if (CollectionUtils.isEmpty(ids) && CollectionUtils.isEmpty(executionIds)) {
      return null;
    }
    if (CollectionUtils.isEmpty(ids) && CollectionUtils.isNotEmpty(executionIds)) {
      return new PrepareQuery(" AND execution.ID IN (:executionIds) ", "executionIds", executionIds);
    }
    if (CollectionUtils.isNotEmpty(ids) && CollectionUtils.isEmpty(executionIds)) {
      return new PrepareQuery(" AND executionGroup.ID IN (:ids) ", "ids", ids);
    }
    return new PrepareQuery(" AND (executionGroup.ID IN (:ids) OR execution.ID IN (:executionIds)) ",
        Map.of("ids", ids, "executionIds", executionIds));
  }

  PrepareQuery buildQueryIdIn(List<String> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return null;
    }
    return new PrepareQuery(" AND executionGroup.ID IN (:ids) ", "ids", ids);
  }


  PrepareQuery buildOrderQuery(PaginationRequestDTO paginationRequest) {
    var sortBy = paginationRequest.getSortBy();
    PrepareQuery query;
    if (StringUtils.isBlank(sortBy)) {
      query = new PrepareQuery(" ORDER BY executionGroup.CREATED_DATE ");
    } else {
      query = switch (sortBy) {
        case "name" -> new PrepareQuery(" ORDER BY executionGroup.NAME ");
        case "description" -> new PrepareQuery(" ORDER BY executionGroup.DESCRIPTION ");
        default -> new PrepareQuery(" ORDER BY executionGroup.CREATED_DATE ");
      };
    }
    return query.append(
        Objects.isNull(paginationRequest.getSortOrder()) ? null : paginationRequest.getSortOrder().name());
  }
}
