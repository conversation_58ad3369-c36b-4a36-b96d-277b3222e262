package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysPermissionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 9/23/2024
 */
@Repository
public interface SysPermissionRepository
    extends JpaCommonRepository<SysPermissionEntity, Long>, SysPermissionRepositoryCustom {
  /**
   * Find all by list role.
   *
   * @param roleIds roleIds
   * @return list SysPermissionEntity
   */
  List<SysPermissionEntity> findAllByRoleIdIn(List<Long> roleIds);

  /**
   * Find all by list role.
   *
   * @param module module permission
   * @param action  action perrmission
   * @return list SysPermissionEntity
   */
  List<SysPermissionEntity> findAllByModuleAndAction(PermissionModuleEnum module, PermissionActionEnum action);

  /**
   * Delete all permission by roleId.
   *
   * @param roleId roleId
   * @return total row
   */
  @Transactional
  int deleteAllByRoleId(Long roleId);

  /**
   * Deletes all permissions based on the provided module, action, type, module parent ID, and module ID.
   *
   * @param module         the module enum
   * @param action         the action enum
   * @param type           the permission type enum
   * @param moduleParentId the ID of the parent module
   * @param moduleId       the ID of the module
   */
  void deleteAllByModuleAndActionAndTypeAndModuleParentIdAndModuleId(
      PermissionModuleEnum module,
      PermissionActionEnum action,
      PermissionTypeEnum type,
      String moduleParentId,
      String moduleId
  );

  /**
   * Deletes all permissions based on the provided module, action, type, module parent ID, and module ID.
   *
   * @param module         the module enum
   * @param action         the action enum
   * @param type           the permission type enum
   * @param moduleParentId the ID of the parent module
   */
  void deleteAllByModuleAndActionAndTypeAndModuleParentId(
      PermissionModuleEnum module,
      PermissionActionEnum action,
      PermissionTypeEnum type,
      String moduleParentId
  );

  /**
   * Updates the module parent ID for permissions based on the specified module, action, type, and old module parent ID.
   *
   * @param module            the specified module enum
   * @param action            the specified action enum
   * @param type              the specified permission type enum
   * @param oldModuleParentId the current parent ID of the module to be updated
   * @param newModuleParentId the new parent ID to update to
   */
  @Modifying
  @Query(value = """
      UPDATE SYS_ROLE_PERMISSION
      SET MODULE_PARENT_ID = :newModuleParentId
      WHERE ACTION = :action
        AND MODULE_NAME = :module
        AND TYPE = :type
        AND MODULE_PARENT_ID = :oldModuleParentId
      """, nativeQuery = true)
  void updateModuleParentIdByModuleAndActionAndType(String module,
                                                    String action,
                                                    String type, String oldModuleParentId,
                                                    String newModuleParentId);

}
