package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ModifyAlertConfigDependencyTypeEnum;

/**
 * Repository table ModifyAlertConfig.
 */
public interface ModifyAlertConfigRepositoryCustom {


  /**
   * get list name of modify alert config.
   *
   * @param dependencyId dependencyId
   * @param type         ModifyAlertConfigDependencyTypeEnum
   * @return list name of modify alert config
   */
  List<String> findDependencyNameByDependencyId(String dependencyId,
                                                List<ModifyAlertConfigDependencyTypeEnum> type);
}
