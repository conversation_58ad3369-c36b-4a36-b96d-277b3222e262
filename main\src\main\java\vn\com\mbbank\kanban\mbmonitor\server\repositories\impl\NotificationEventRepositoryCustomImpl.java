package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.util.StringUtils;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.utils.KanbanEntityUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEventEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.NotificationEventRepositoryCustom;

/**
 * Implementation of NotificationEventRepositoryCustom.
 */
@Slf4j
@RequiredArgsConstructor
public class NotificationEventRepositoryCustomImpl implements NotificationEventRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public Page<NotificationEventEntity> findAll(PaginationRequestDTO paginationRequest) {
    var query = new PrepareQuery("""
        SELECT *
        FROM NOTIFICATION_EVENT notificationEvent
        WHERE 1=1
        """
    ).append(buildQuerySearchLike(paginationRequest.getSearch()))
        .append(buildOrderQuery(paginationRequest));

    Pageable pageable = PageRequest.of(paginationRequest.getPage(),
        paginationRequest.getSize());

    return sqlQueryUtil.queryModel()
        .queryPaging(query.getQueryBuilder().toString(), query.getParams(),
            NotificationEventEntity.class, pageable);
  }

  /**
   * Build query for search.
   *
   * @param search Search string
   * @return PrepareQuery with search condition
   */
  private PrepareQuery buildQuerySearchLike(String search) {
    if (!StringUtils.hasText(search)) {
      return null;
    }
    return new PrepareQuery("AND (")
        .append(new PrepareQuery("LOWER(notificationEvent.TITLE) LIKE :search"),
            LikeMatcher.CONTAINING)
        .append(new PrepareQuery(" OR LOWER(notificationEvent.CONTENT) LIKE :search",
                Map.of("search", search.toLowerCase())),
            LikeMatcher.CONTAINING)
        .append(")");
  }

  /**
   * Build order query.
   *
   * @param paginationRequest Pagination request
   * @return PrepareQuery with order condition
   */
  private PrepareQuery buildOrderQuery(PaginationRequestDTO paginationRequest) {
    String sortColumn = KanbanEntityUtils.getColumnName(paginationRequest.getSortBy(),
        NotificationEventEntity.class);

    if (!StringUtils.hasText(sortColumn)) {
      return new PrepareQuery(" ORDER BY notificationEvent.CREATED_DATE DESC");
    }

    var query = new PrepareQuery(" ORDER BY notificationEvent.").append(sortColumn);

    if (paginationRequest.getSortOrder() != null) {
      query.append(" ").append(paginationRequest.getSortOrder().name());
    }

    return query;
  }
}