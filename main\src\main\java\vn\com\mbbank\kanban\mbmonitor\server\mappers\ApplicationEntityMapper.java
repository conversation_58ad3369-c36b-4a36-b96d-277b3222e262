package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ApplicationRequest;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 16/9/2024
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ApplicationEntityMapper
    extends KanbanBaseMapper<ApplicationEntity, ApplicationRequest> {
  ApplicationEntityMapper INSTANCE = Mappers.getMapper(ApplicationEntityMapper.class);
}
