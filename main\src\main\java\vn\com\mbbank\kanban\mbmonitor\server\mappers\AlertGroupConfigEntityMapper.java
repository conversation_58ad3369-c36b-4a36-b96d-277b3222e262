package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertGroupConfigRequest;

/**
 * Mapper logic AlertGroupConfigResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AlertGroupConfigEntityMapper
    extends KanbanBaseMapper<AlertGroupConfigEntity, AlertGroupConfigRequest> {
  AlertGroupConfigEntityMapper INSTANCE = Mappers.getMapper(AlertGroupConfigEntityMapper.class);

  /**
   * map from AlertPriorityConfigEntity to AlertPriorityConfigResponse.
   *
   * @param entity  AlertGroupConfigEntity.
   * @param request AlertGroupConfigRequest.
   */
  void merge(@MappingTarget AlertGroupConfigEntity entity, AlertGroupConfigRequest request);
}
