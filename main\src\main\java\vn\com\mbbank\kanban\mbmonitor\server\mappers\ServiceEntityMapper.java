package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ServiceRequest;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 16/9/2024
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ServiceEntityMapper extends KanbanBaseMapper<ServiceEntity, ServiceRequest> {
  ServiceEntityMapper INSTANCE = Mappers.getMapper(ServiceEntityMapper.class);

}


