package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.AlertGroupNoteAmountModel;

/**
 * Custom Repository table Note.
 */
public interface NoteRepositoryCustom {

  /**
   * count number of note create in the past.
   *
   * @param alertGroupIds alertIds
   * @param secondAgo     minute range
   * @return list of amount note
   */
  List<AlertGroupNoteAmountModel> countNoteCreateInSecondAgoByAlertGroupIdIn(List<Long> alertGroupIds, int secondAgo);
}
