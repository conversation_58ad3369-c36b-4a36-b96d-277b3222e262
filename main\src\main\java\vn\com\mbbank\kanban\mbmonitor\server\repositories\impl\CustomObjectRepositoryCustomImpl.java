package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.List;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.CustomObjectRepositoryCustom;

/**
 * Implement EmailPartnerRepositoryCustom table EMAIL_PARTNER.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CustomObjectRepositoryCustomImpl implements CustomObjectRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public List<CustomObjectEntity> findAllByAlertGroupConfigId(Long alertGroupConfigId) {
    var query = new PrepareQuery("""
          SELECT customObject.*
        FROM CUSTOM_OBJECT customObject
            JOIN ALERT_GROUP_CONFIG_CONDITION alertGroupConfigCondition
                 ON customObject.ID = alertGroupConfigCondition.CUSTOM_OBJECT_ID
        WHERE alertGroupConfigCondition.ALERT_GROUP_CONFIG_ID = :alertGroupConfigId
          """, "alertGroupConfigId", alertGroupConfigId);
    return sqlQueryUtil.queryModel().queryForList(query.getQuery(), query.getParams(), CustomObjectEntity.class);
  }
}
