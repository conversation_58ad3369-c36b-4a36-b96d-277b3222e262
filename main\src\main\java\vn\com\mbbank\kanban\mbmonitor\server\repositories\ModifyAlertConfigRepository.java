package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ModifyAlertConfigEntity;

/**
 * Repository interface for managing modify alert configuration data in the system.
 */
@Repository
public interface ModifyAlertConfigRepository
    extends JpaCommonRepository<ModifyAlertConfigEntity, Long>, ModifyAlertConfigRepositoryCustom {

  /**
   * find All order by position.
   *
   * @return list entity modifyAlertConfig
   */
  List<ModifyAlertConfigEntity> findAllByOrderByPosition();

  /**
   * get all config by name.
   *
   * @param name name.
   * @return a list of config.
   */
  List<ModifyAlertConfigEntity> findAllByNameContainingIgnoreCaseOrderByPosition(String name);

  /**
   * check modify alert config existed by id, name and deleted status.
   *
   * @param id   id of config
   * @param name name of config
   * @return existed or not
   */
  boolean existsByIdNotAndNameIgnoreCase(Long id, String name);

  /**
   * check modify alert config existed by name.
   *
   * @param name name of config
   * @return existed or not
   */
  boolean existsByNameIgnoreCase(String name);


  /**
   * find all modify alert config by active.
   *
   * @param active active status
   * @return next position.
   */
  List<ModifyAlertConfigEntity> findAllByActive(Boolean active);

  /**
   * get all config in range.
   *
   * @param fromPosition a position.
   * @param toPosition   a position.
   * @return a list of config with position in range.
   */
  List<ModifyAlertConfigEntity> findAllByPositionBetween(Integer fromPosition, Integer toPosition);

  /**
   * get next position.
   *
   * @return next position.
   */
  @Query(value = "SELECT MODIFY_ALERT_CONFIG_POSITION_SEQ.nextval FROM dual", nativeQuery = true)
  int getNextPositionValue();
}
