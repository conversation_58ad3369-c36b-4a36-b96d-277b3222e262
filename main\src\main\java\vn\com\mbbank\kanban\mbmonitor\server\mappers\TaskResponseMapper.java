package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TaskEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.TaskResponse;

/**
 * TaskResponseMapper.
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TaskResponseMapper extends KanbanBaseMapper<TaskResponse, TaskEntity> {
  TaskResponseMapper INSTANCE = Mappers.getMapper(TaskResponseMapper.class);
}