package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.collections4.CollectionUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertDurationCompareOperator;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertDurationCompareUnit;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertCursor;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AlertResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.AlertRepositoryCustom;

/**
 * Implement AlertRepositoryCustom table Alert.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AlertRepositoryCustomImpl implements AlertRepositoryCustom {
  SqlQueryUtil sqlQueryUtil;

  @Override
  public List<AlertEntity> findAllSingleAlertByServiceIdInAndApplicationIdInCreatedDateAndStatus(
      List<String> serviceIds,
      List<String> applicationIds,
      int alertGroupHandleTriggerInterval,
      AlertGroupStatusEnum status) {
    var query = new PrepareQuery("""
        SELECT
            alert.ID as id,
            alert.CONTENT as content,
            alert.RECIPIENT as recipient,
            alert.ALERT_PRIORITY_CONFIG_ID as alertPriorityConfigId,
            alert.STATUS as status,
            alert.SERVICE_ID as serviceId,
            alert.APPLICATION_ID as applicationId,
            alert.ALERT_GROUP_ID as alertGroupId,
            alert.CREATED_DATE as createdDate,
            alert.CLOSED_DATE as closedDate,
            alert.CLOSED_BY as closedBy
        FROM ALERT alert
        LEFT JOIN ALERT_GROUP alertGroup
        ON alert.ID = alertGroup.PRIMARY_ALERT_ID
        WHERE alertGroup.ALERT_GROUP_CONFIG_ID IS NULL
        """)
        .append(buildQueryServiceIdEqual(serviceIds))
        .append(buildQueryApplicationIdEqual(applicationIds))
        .append(buildQueryGroupStatusEq(status))
        .append(buildCreateTimeGreaterThan(alertGroupHandleTriggerInterval));

    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), AlertEntity.class);
  }

  @Override
  public List<AlertEntity> findTopAlertsByAlertGroupIdNullAndStatus(AlertStatusEnum status,
                                                                    int numberOfResult) {
    var query = new PrepareQuery("""
        SELECT *
        FROM ALERT alert
        WHERE alert.ALERT_GROUP_ID IS NULL
        """);
    if (Objects.nonNull(status)) {
      query.append(" AND alert.STATUS= :status", "status", status.name());
    }
    query.append(" ORDER BY alert.ID ASC"
        + " OFFSET 1 ROW FETCH FIRST " + numberOfResult + " ROWS ONLY");
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), AlertEntity.class);
  }

  @Override
  public CursorPageResponse<AlertResponse, AlertCursor> findAll(AlertPaginationRequest paginationRequest) {
    var query = new PrepareQuery(
        """
            SELECT alert.ID,
                  alert.CONTENT,
                  alert.RECIPIENT,
                  alert.STATUS,
                  alert.CREATED_DATE AS createdDate,
                  alert.ALERT_GROUP_ID AS alertGroupId,
                  alert.ALERT_PRIORITY_CONFIG_ID AS priorityConfigId,
                  alertPriorityConfig.NAME as priorityName,
                  alertPriorityConfig.COLOR as priorityColor,
                  alert.SERVICE_ID AS serviceId,
                  service.NAME AS serviceName,
                  alert.APPLICATION_ID AS applicationId,
                  application.NAME as applicationName,
                  alert.CLOSED_DATE as closedDate,
                  alert.CLOSED_BY as closedBy,
                  alert.CLOSED_DURATION as closedDuration
            FROM ALERT alert
            LEFT JOIN SERVICE service ON service.ID = alert.SERVICE_ID
            LEFT JOIN APPLICATION application ON application.ID = alert.APPLICATION_ID
            LEFT JOIN ALERT_PRIORITY_CONFIG alertPriorityConfig
              ON alertPriorityConfig.ID = alert.ALERT_PRIORITY_CONFIG_ID
            WHERE 1=1
            """
    )
        .append(buildQueryRangeDate(paginationRequest.getFromDate(), paginationRequest.getToDate()))
        .append(buildQueryStatusIn(paginationRequest.getStatuses()))
        .append(buildQueryPriorityConfigIdIn(paginationRequest.getAlertPriorityConfigIds()))
        .append(buildQueryServiceIdIn(paginationRequest.getServiceIds()))
        .append(buildAlertGroupIdEqual(paginationRequest.getAlertGroupId()))
        .append(buildQueryApplicationIdIn(paginationRequest.getApplicationIds()))
        .append(buildQueryContentLike(paginationRequest.getContent()), LikeMatcher.CONTAINING)
        .append(buildQueryRecipientLike(paginationRequest.getRecipient()), LikeMatcher.CONTAINING)
        .append(buildQueryClosedByIn(paginationRequest.getClosedBy()))
        .append(buildClosedDuration(paginationRequest.getCloseDurationOperator(), paginationRequest.getClosedDuration(),
            paginationRequest.getCloseDurationUnit()))
        .append(buildQueryCursor(paginationRequest.getCursorAlertId(), paginationRequest.getCursorAlertCreatedDate()))
        .append(" ORDER BY alert.CREATED_DATE DESC, alert.ID DESC")
        .append(" FETCH FIRST " + (paginationRequest.getPageSize() + 1) + " ROWS ONLY ");

    // lấy dư 1 phần tử để xem có page tiếp theo không.
    var result = sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), AlertResponse.class);
    if (result.size() > paginationRequest.getPageSize()) {
      return new CursorPageResponse<>(result.subList(0, paginationRequest.getPageSize()),
          new AlertCursor(result.get(result.size() - 2).getId(), result.get(result.size() - 2).getCreatedDate())
      );
    }
    return new CursorPageResponse<>(result, null);
  }

  @Override
  public Long countAllAlert(AlertPaginationRequest paginationRequest) {
    var query = new PrepareQuery(
        """
            SELECT COUNT(alert.ID)
            FROM ALERT alert
            WHERE 1=1
            """
    )
        .append(buildQueryRangeDate(paginationRequest.getFromDate(), paginationRequest.getToDate()))
        .append(buildQueryStatusIn(paginationRequest.getStatuses()))
        .append(buildQueryPriorityConfigIdIn(paginationRequest.getAlertPriorityConfigIds()))
        .append(buildQueryServiceIdIn(paginationRequest.getServiceIds()))
        .append(buildAlertGroupIdEqual(paginationRequest.getAlertGroupId()))
        .append(buildQueryApplicationIdIn(paginationRequest.getApplicationIds()))
        .append(buildQueryContentLike(paginationRequest.getContent()), LikeMatcher.CONTAINING)
        .append(buildQueryRecipientLike(paginationRequest.getRecipient()), LikeMatcher.CONTAINING)
        .append(buildQueryClosedByIn(paginationRequest.getClosedBy()))
        .append(buildClosedDuration(paginationRequest.getCloseDurationOperator(), paginationRequest.getClosedDuration(),
            paginationRequest.getCloseDurationUnit()));

    return sqlQueryUtil.queryModel()
        .queryForObject(query.getQuery(), query.getParams(), Long.class);
  }

  private PrepareQuery buildClosedDuration(AlertDurationCompareOperator operator, Long closedDuration,
                                           AlertDurationCompareUnit unit) {
    if (Objects.isNull(operator) || Objects.isNull(closedDuration) || Objects.isNull(unit)) {
      return null;
    }

    var durationSecond = switch (unit) {
      case HOUR -> closedDuration * 60 * 60;
      case MINUTE -> closedDuration * 60;
      case SECOND -> closedDuration;
    };

    return new PrepareQuery(" AND alert.CLOSED_DURATION "
        + operator.getOperator()
        + " :closedDurationSecond AND alert.CLOSED_DURATION IS NOT NULL ",
        "closedDurationSecond", durationSecond);
  }

  private PrepareQuery buildAlertGroupIdEqual(Long alertGroupId) {
    if (Objects.isNull(alertGroupId)) {
      return null;
    }
    return new PrepareQuery("AND alert.ALERT_GROUP_ID = :alertGroupId ", "alertGroupId", alertGroupId);
  }

  private PrepareQuery buildQueryCursor(Long cursorAlertId, String cursorCreatedDate) {
    if (Objects.isNull(cursorAlertId) || StringUtils.isBlank(cursorCreatedDate)) {
      return null;
    }
    return new PrepareQuery("""
        AND (alert.CREATED_DATE < :createDate
          OR (alert.CREATED_DATE = :createDate AND alert.ID < :alertId))
        """, Map.of("createDate", Timestamp.valueOf(cursorCreatedDate),
        "alertId",
        cursorAlertId));
  }

  private PrepareQuery buildQueryClosedByIn(List<String> closedBy) {
    if (CollectionUtils.isEmpty(closedBy)) {
      return null;
    }
    return new PrepareQuery(" AND alert.CLOSED_BY IN (:closedBy) ", "closedBy", closedBy);
  }

  private PrepareQuery buildQueryServiceIdEqual(List<String> serviceIds) {
    if (CollectionUtils.isEmpty(serviceIds)) {
      return null;
    }
    return new PrepareQuery("AND alertGroup.SERVICE_IDS IN (:serviceIds) ",
        Map.of("serviceIds", serviceIds));
  }

  private PrepareQuery buildQueryApplicationIdEqual(List<String> applicationIds) {
    if (CollectionUtils.isEmpty(applicationIds)) {
      return null;
    }
    return new PrepareQuery("AND alertGroup.APPLICATION_IDS IN (:applicationIds) ",
        Map.of("applicationIds", applicationIds));
  }

  private PrepareQuery buildCreateTimeGreaterThan(int alertGroupHandleTriggerInterval) {
    var past = org.apache.commons.lang3.time.DateUtils.addSeconds(new Date(),
        -alertGroupHandleTriggerInterval);
    return new PrepareQuery(" AND alertGroup.CREATED_DATE >= :createdDate",
        Map.of("createdDate", past));
  }

  PrepareQuery buildQueryContentLike(String content) {
    if (StringUtils.isBlank(content)) {
      return null;
    }
    return new PrepareQuery(" AND LOWER(alert.CONTENT) LIKE :content ",
        "content", content.toLowerCase());
  }

  PrepareQuery buildQueryStatusIn(List<AlertStatusEnum> alertStatuses) {
    if (CollectionUtils.isEmpty(alertStatuses)) {
      return null;
    }
    return new PrepareQuery(" AND alert.STATUS IN (:statuses) ", "statuses",
        alertStatuses.stream().map(AlertStatusEnum::toString).collect(Collectors.toList()));
  }

  PrepareQuery buildQueryPriorityConfigIdIn(List<Long> alertPriorityConfigIds) {
    if (CollectionUtils.isEmpty(alertPriorityConfigIds)) {
      return null;
    }
    return new PrepareQuery(" AND alert.ALERT_PRIORITY_CONFIG_ID IN (:alertPriorityConfigIds) ",
        "alertPriorityConfigIds",
        alertPriorityConfigIds);
  }

  PrepareQuery buildQueryServiceIdIn(List<String> serviceIds) {
    if (CollectionUtils.isEmpty(serviceIds)) {
      return null;
    }
    return new PrepareQuery(" AND alert.SERVICE_ID IN (:serviceIds) ", "serviceIds", serviceIds);
  }

  PrepareQuery buildQueryRecipientLike(String recipient) {
    if (StringUtils.isBlank(recipient)) {
      return null;
    }
    return new PrepareQuery(" AND LOWER(alert.RECIPIENT) LIKE :recipient ",
        "recipient", recipient.toLowerCase());
  }

  PrepareQuery buildQueryApplicationIdIn(List<String> applicationIds) {
    if (CollectionUtils.isEmpty(applicationIds)) {
      return null;
    }
    return new PrepareQuery(" AND alert.APPLICATION_ID IN (:applicationIds)", "applicationIds",
        applicationIds);
  }

  PrepareQuery buildQueryRangeDate(String fromDate, String toDate) {

    if (StringUtils.isNoneBlank(fromDate, toDate)) {
      return new PrepareQuery(" AND alert.CREATED_DATE BETWEEN :fromDate AND :toDate ",
          Map.of("fromDate", DateUtils.convertStringToDate(fromDate),
              "toDate", DateUtils.convertStringToDate(toDate)));
    }
    return null;
  }

  PrepareQuery buildQueryGroupStatusEq(AlertGroupStatusEnum status) {
    if (Objects.isNull(status)) {
      return null;
    }
    return new PrepareQuery(" AND alertGroup.STATUS = :status", "status", status.name());
  }
}
