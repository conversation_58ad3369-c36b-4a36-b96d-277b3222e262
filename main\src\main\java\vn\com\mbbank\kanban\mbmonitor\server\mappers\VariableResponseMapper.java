package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.constants.ExecutionConstants;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.VariableResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;

/**
 * VariableResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VariableResponseMapper
    extends KanbanBaseMapper<VariableResponse, VariableEntity> {
  VariableResponseMapper INSTANCE = Mappers.getMapper(VariableResponseMapper.class);

  /**
   * map VariableEntity toVariableResponse .
   *
   * @param source VariableEntity
   * @return VariableResponse
   */
  @Mapping(target = "value", expression = "java(convertValue(source))")
  VariableResponse map(VariableEntity source);

  /**
   * convertValue.
   *
   * @param source VariableEntity
   * @return string
   */
  default String convertValue(VariableEntity source) {
    return source.isHidden() ? ExecutionConstants.HIDDEN_VARIABLE_PLACEHOLDER : source.getValue();
  }
}
