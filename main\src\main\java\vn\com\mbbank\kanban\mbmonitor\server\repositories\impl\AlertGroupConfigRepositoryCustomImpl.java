package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupConfigDependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.AlertGroupConditionDependenciesModel;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.AlertGroupConfigRepositoryCustom;

/**
 * AlertGroupConfigRepositoryCustomImpl.
 */
@RequiredArgsConstructor
public class AlertGroupConfigRepositoryCustomImpl
    implements AlertGroupConfigRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public List<AlertGroupConfigEntity> findAllByServiceIdAndApplicationIdAndDeletedAndActive(
      String serviceId,
      String applicationId,
      Boolean deleted,
      Boolean active) {
    var query = new PrepareQuery("""
        SELECT *
        FROM ALERT_GROUP_CONFIG alertGroupConfig
        WHERE 1=1
        """)
        .append(buildServiceIdsLike(serviceId), LikeMatcher.CONTAINING)
        .append(buildApplicationIdsLike(applicationId), LikeMatcher.CONTAINING)
        .append(buildDeletedEqual(deleted))
        .append(buildActiveEqual(active));

    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), AlertGroupConfigEntity.class);
  }

  @Override
  public List<AlertGroupConfigEntity> findAllByDeletedAndSearch(Boolean deleted, String search) {
    var query = new PrepareQuery("""
        SELECT *
        FROM ALERT_GROUP_CONFIG alertGroupConfig
        WHERE 1=1
        """)
        .append(buildDeletedEqual(deleted))
        .append(buildNameOrDescriptionLike(search));

    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), AlertGroupConfigEntity.class);
  }

  @Override
  public List<AlertGroupConditionDependenciesModel> findAllByNotDeletedAndCustomObjectNull() {
    String sql = """
        SELECT alertGroupConfigCondition.RULE_GROUP AS rules, alertGroupConfig.name
        FROM ALERT_GROUP_CONFIG_CONDITION alertGroupConfigCondition
        JOIN ALERT_GROUP_CONFIG alertGroupConfig
        ON alertGroupConfigCondition.ALERT_GROUP_CONFIG_ID = alertGroupConfig.ID
        WHERE alertGroupConfig.DELETED = 0 AND  alertGroupConfigCondition.CUSTOM_OBJECT_ID IS NULL
        """;
    return sqlQueryUtil.queryModel()
        .queryForList(sql, AlertGroupConditionDependenciesModel.class);
  }

  @Override
  public List<AlertGroupConditionDependenciesModel> findAllByNotDeleted() {
    String sql = """
        SELECT alertGroupConfigCondition.RULE_GROUP AS rules, alertGroupConfig.name,
        alertGroupConfigCondition.CUSTOM_OBJECT_ID
        FROM ALERT_GROUP_CONFIG_CONDITION alertGroupConfigCondition
                 JOIN ALERT_GROUP_CONFIG alertGroupConfig
                      ON alertGroupConfigCondition.ALERT_GROUP_CONFIG_ID = alertGroupConfig.ID
        WHERE alertGroupConfig.DELETED = 0
        """;
    return sqlQueryUtil.queryModel()
        .queryForList(sql, AlertGroupConditionDependenciesModel.class);
  }

  @Override
  public List<String> findDependencyNameByDependencyId(String dependencyId,
                                                       List<AlertGroupConfigDependencyTypeEnum> type) {
    var query = new PrepareQuery("""
        SELECT alertGroupConfig.name FROM ALERT_GROUP_CONFIG alertGroupConfig
        LEFT JOIN ALERT_GROUP_CONFIG_DEPENDENCY alertGroupConfigDependency
          ON alertGroupConfig.ID = alertGroupConfigDependency.ALERT_GROUP_CONFIG_ID
        WHERE alertGroupConfig.DELETED = 0
        """);
    if (StringUtils.isNotBlank(dependencyId)) {
      query.append(" AND alertGroupConfigDependency.DEPENDENCE_ID = :dependencyId", "dependencyId",
          dependencyId);
    }
    if (Objects.nonNull(type)) {
      query.append(" AND alertGroupConfigDependency.TYPE IN (:type)", "type",
          type.stream().map(Enum::name).toList());
    }
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), String.class);
  }

  @Override
  public Integer findRealPositionById(Long id) {
    var query = new PrepareQuery(
        """
            SELECT POSITION
              FROM (SELECT ROW_NUMBER() OVER (ORDER BY alertGroupConfig.POSITION) AS POSITION, ID
                    FROM ALERT_GROUP_CONFIG alertGroupConfig
                    WHERE alertGroupConfig.DELETED = 0)
              WHERE ID = :id
            """, "id", id);
    return sqlQueryUtil.queryModel()
        .queryForObject(query.getQuery(), query.getParams(), Integer.class);
  }

  private PrepareQuery buildNameOrDescriptionLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    return new PrepareQuery(" AND (")
        .append(new PrepareQuery("alertGroupConfig.NAME LIKE :search", "search", search),
            LikeMatcher.CONTAINING)
        .append(" OR ")
        .append(new PrepareQuery("alertGroupConfig.DESCRIPTION LIKE :search", "search", search),
            LikeMatcher.CONTAINING)
        .append(" OR ")
        .append(new PrepareQuery("alertGroupConfig.ALERT_OUTPUT LIKE :search", "search", search),
            LikeMatcher.CONTAINING)
        .append(" OR ")
        .append(new PrepareQuery("alertGroupConfig.TYPE LIKE :search", "search", search),
            LikeMatcher.CONTAINING)
        .append(")");
  }

  private PrepareQuery buildServiceIdsLike(String serviceId) {
    if (StringUtils.isBlank(serviceId)) {
      return null;
    }
    return new PrepareQuery("AND alertGroupConfig.SERVICE_IDS LIKE :serviceId ",
        Map.of("serviceId", serviceId));
  }

  private PrepareQuery buildApplicationIdsLike(String applicationId) {
    if (StringUtils.isBlank(applicationId)) {
      return null;
    }
    return new PrepareQuery("AND alertGroupConfig.APPLICATION_IDS LIKE :applicationId ",
        Map.of("applicationId", applicationId));
  }

  private PrepareQuery buildDeletedEqual(Boolean deleted) {
    if (Objects.isNull(deleted)) {
      return null;
    }
    return new PrepareQuery("AND alertGroupConfig.DELETED = :deleted ",
        Map.of("deleted", deleted));
  }

  private PrepareQuery buildActiveEqual(Boolean active) {
    if (Objects.isNull(active)) {
      return null;
    }
    return new PrepareQuery("AND alertGroupConfig.ACTIVE = :active ",
        Map.of("active", active ? 1 : 0));
  }
}
