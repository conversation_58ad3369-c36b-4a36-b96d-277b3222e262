package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigConditionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigDependencyEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupConfigDependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupOutputEnum;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AlertGroupConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ApplicationResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ServiceResponse;

/**
 * Mapper logic AlertGroupConfigResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AlertGroupConfigResponseMapper extends
    KanbanBaseMapper<AlertGroupConfigResponse, AlertGroupConfigEntity> {
  AlertGroupConfigResponseMapper INSTANCE = Mappers.getMapper(AlertGroupConfigResponseMapper.class);


  /**
   * map from AlertPriorityConfigEntity to AlertPriorityConfigResponse.
   *
   * @param entity       AlertGroupConfigEntity.
   * @param conditions   list of AlertGroupConfigConditionEntity.
   * @param services     list of service
   * @param applications list of application
   * @param dependencies dependencies of config
   * @return AlertGroupConfigResponse
   */
  default AlertGroupConfigResponse map(AlertGroupConfigEntity entity,
                                       List<AlertGroupConfigConditionEntity> conditions,
                                       List<ServiceEntity> services,
                                       List<ApplicationResponse> applications,
                                       List<AlertGroupConfigDependencyEntity> dependencies) {

    var alertGroupConfigResponse = this.map(entity);
    if (CollectionUtils.isEmpty(conditions)) {
      return alertGroupConfigResponse;
    }
    if (AlertGroupConfigTypeEnum.MULTIPLE_CONDITION.equals(entity.getType())) {
      alertGroupConfigResponse.setRuleGroups(
          conditions.stream().map(AlertGroupConfigConditionEntity::getRuleGroup).toList());
    }
    if (AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE.equals(entity.getType())) {
      alertGroupConfigResponse.setCustomObjectIds(
          conditions.stream().map(AlertGroupConfigConditionEntity::getCustomObjectId).toList());
    }
    var dependencyMap = dependencies.stream()
        .collect(Collectors.toMap(AlertGroupConfigDependencyEntity::getDependencyId, Function.identity()));
    var serviceDependencies = new ArrayList<ServiceResponse>();
    for (ServiceEntity service : services) {
      if (dependencyMap.containsKey(service.getId())) {
        var type = dependencyMap.get(service.getId()).getType();
        if (AlertGroupConfigDependencyTypeEnum.SERVICE_WITH_ALL_APPLICATION.equals(type)
            || AlertGroupConfigDependencyTypeEnum.SERVICE.equals(type)) {
          serviceDependencies.add(ServiceResponseMapper.INSTANCE.map(service));
        }
      }
      if (AlertGroupOutputEnum.CUSTOM.equals(entity.getAlertOutput())
          && StringUtils.isNoneBlank(entity.getCustomServiceId())
          && Objects.equals(entity.getCustomServiceId(), service.getId())) {
        alertGroupConfigResponse.setCustomService(
            ServiceResponseMapper.INSTANCE.map(service));
      }
    }

    alertGroupConfigResponse.setServices(serviceDependencies);
    alertGroupConfigResponse.setApplications(
        applications.stream().filter(application -> dependencyMap.containsKey(application.getId())).toList());
    if (AlertGroupOutputEnum.CUSTOM.equals(entity.getAlertOutput())
        && StringUtils.isNoneBlank(entity.getCustomApplicationId())) {
      alertGroupConfigResponse.setCustomApplication(
          applications.stream()
              .filter(application -> Objects.equals(application.getId(), entity.getCustomApplicationId())).findFirst()
              .get());
    }
    return alertGroupConfigResponse;
  }
}
