package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysPermissionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/4/2024
 */
public interface SysPermissionRepositoryCustom {
  /**
   * Find data SysPermissionEntity.
   *
   * @param userId                userId
   * @param module                module
   * @param permissionActionEnums permissionActionEnums
   * @return list SysPermissionEntity
   */
  List<SysPermissionEntity> findAllByUserIdAndModuleInAndActionIn(Long userId,
                                                                  List<PermissionModuleEnum> module,
                                                                  List<PermissionActionEnum> permissionActionEnums);
}
