package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WebHookEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.WebHookDto;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 12/03/2024
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WebHookResponseMapper extends KanbanBaseMapper<WebHookDto, WebHookEntity> {
  WebHookResponseMapper INSTANCE = Mappers.getMapper(WebHookResponseMapper.class);
}
