package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.Map;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEntityUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.AlertRequestResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertRequestEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.AlertRequestRepositoryCustom;

/**
 * Implement AlertRequestRepositoryCustom table AlertRequest.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AlertRequestRepositoryCustomImpl implements AlertRequestRepositoryCustom {
  SqlQueryUtil sqlQueryUtil;


  @Override
  public AlertRequestResponse findWithId(String id) {
    var query = new PrepareQuery(
        """
            SELECT alertRequest.ID,
                   alertRequest.CONTENT,
                   alertRequest.CONTENT_JSON,
                   alertRequest.RECIPIENT,
                   alertRequest.STATUS,
                   alertRequest.SOURCE_TYPE,
                   alertRequest.PRIORITY_ID,
                   alertRequest.CREATED_BY,
                   alertRequest.CRON_TIME,
                   alertRequest.CONDITION_VALUE,
                   alertRequest.CONDITION_OPERATOR,
                   alertRequest.APPLICATION_ID,
                   alertRequest.SERVICE_ID,
                   alertRequest.REJECTED_REASON,
                   application.NAME AS applicationName,
                   service.NAME AS serviceName
            FROM ALERT_REQUEST alertRequest
            JOIN APPLICATION application ON application.ID= alertRequest.APPLICATION_ID
            JOIN SERVICE service ON service.ID= alertRequest.SERVICE_ID
            """
    ).append("WHERE alertRequest.ID= :id", "id", id);
    return sqlQueryUtil.queryModel()
        .queryForObject(query.getQuery(), query.getParams(), AlertRequestResponse.class);
  }

  @Override
  public Page<AlertRequestResponse> findAll(PaginationRequestDTO paginationRequest) {
    var query = new PrepareQuery("""
        SELECT alertRequest.ID AS id, alertRequest.STATUS AS status,
        alertRequest.SOURCE_TYPE AS sourceType , alertRequest.RECIPIENT AS recipient,
        alertRequest.CREATED_BY AS createdBy,
        service.NAME AS serviceName, application.NAME AS applicationName
        FROM ALERT_REQUEST alertRequest
        JOIN SERVICE service
        ON service.ID = alertRequest.SERVICE_ID
        JOIN APPLICATION application
        ON application.ID = alertRequest.APPLICATION_ID
        WHERE 1=1
        """
    ).append(buildQuerySearchLike(paginationRequest.getSearch()));

    String sortColumn = KanbanEntityUtils.getColumnName(paginationRequest.getSortBy(),
        AlertRequestEntity.class);
    if (!KanbanCommonUtil.isEmpty(sortColumn)) {
      query.append(" ORDER BY alertRequest.").append(sortColumn);
    } else if ("serviceName".equals(paginationRequest.getSortBy())) {
      query.append(" ORDER BY service.NAME");
    } else if ("applicationName".equals(paginationRequest.getSortBy())) {
      query.append(" ORDER BY application.NAME");
    }
    if (paginationRequest.getSortOrder() != null) {
      query.append(" ").append(paginationRequest.getSortOrder().name());
    }
    Pageable pageable = PageRequest.of(paginationRequest.getPage(),
        paginationRequest.getSize());
    return sqlQueryUtil.queryModel()
        .queryPaging(query.getQueryBuilder().toString(), query.getParams(),
            AlertRequestResponse.class, pageable);

  }

  PrepareQuery buildQuerySearchLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    PrepareQuery prepareQuery = new PrepareQuery();
    prepareQuery.append(new PrepareQuery(" AND (LOWER(service.NAME) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(application.NAME) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(alertRequest.RECIPIENT) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(alertRequest.STATUS) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(alertRequest.SOURCE_TYPE) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(alertRequest.CREATED_BY) LIKE :search",
        Map.of("search", search.toLowerCase())), LikeMatcher.CONTAINING);
    prepareQuery.append(" )");
    return prepareQuery;
  }

}
