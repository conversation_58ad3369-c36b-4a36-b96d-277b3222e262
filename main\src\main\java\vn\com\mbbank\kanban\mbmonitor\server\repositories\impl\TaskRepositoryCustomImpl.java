package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TaskEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.DateUtils;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.DateRangeRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.TaskCursor;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.TaskSearchParamRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.TaskSearchRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.TaskResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.TaskRepositoryCustom;

/**
 * TaskRepositoryCustomImpl.
 */
@RequiredArgsConstructor
public class TaskRepositoryCustomImpl implements TaskRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public CursorPageResponse<TaskResponse, TaskCursor> findAllWithPaging(TaskSearchRequest searchRequest,
                                                                        TaskSearchParamRequest requestParam) {

    var query = new PrepareQuery("""
        SELECT task.ID,
               task.NAME,
               task.DESCRIPTION,
               task.STATUS,
               task.START_TIME,
               task.END_TIME,
               task.TYPE,
               task.TIME_TYPE,
               task.CREATED_BY,
               task.CURRENT_ASSIGNEE_USER_NAME,
               task.DELETED,
               task.CREATED_DATE
        FROM TASK task
        WHERE DELETED = 0
        """)
        .append(buildSearchLike(searchRequest.getSearch()), LikeMatcher.CONTAINING)
        .append(buildTypeIn(searchRequest.getTypes()))
        .append(buildStatusIn(searchRequest.getStatuses()))
        .append(buildAssigneeUserIdIn(searchRequest.getAssigneeUsers()))
        .append(buildCreatorUserIdIn(searchRequest.getCreatorUsers()))
        .append(buildDateBetween(searchRequest.getDateRanges()))
        .append(buildQueryCursor(requestParam.getId(), requestParam.getCreatedDate()))
        .append(" ORDER BY task.CREATED_DATE DESC, task.ID DESC")
        .append(" FETCH FIRST " + (requestParam.getPageSize() + 1) + " ROWS ONLY ");

    // lấy dư 1 phần tử để xem có page tiếp theo không.
    var result = sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), TaskResponse.class);
    if (result.size() > requestParam.getPageSize()) {
      return new CursorPageResponse<>(result.subList(0, requestParam.getPageSize()),
          new TaskCursor(result.get(result.size() - 2).getId(), result.get(result.size() - 2).getCreatedDate())
      );
    }
    return new CursorPageResponse<>(result, null);
  }

  @Override
  public List<TaskEntity> findAll(TaskSearchRequest searchRequest) {
    var query = new PrepareQuery("""
        SELECT task.*
        FROM TASK task
        WHERE DELETED = 0
        """)
        .append(buildSearchLike(searchRequest.getSearch()), LikeMatcher.CONTAINING)
        .append(buildTypeIn(searchRequest.getTypes()))
        .append(buildStatusIn(searchRequest.getStatuses()))
        .append(buildAssigneeUserIdIn(searchRequest.getAssigneeUsers()))
        .append(buildCreatorUserIdIn(searchRequest.getCreatorUsers()))
        .append(buildDateBetween(searchRequest.getDateRanges()));

    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), TaskEntity.class);
  }

  @Override
  public List<TaskResponse> findAllByParentTaskIdAndTypeAndStatus(Long parentTaskId, TaskTypeEnum type) {
    var query = new PrepareQuery("""
        SELECT task.ID,
               task.NAME,
               task.DESCRIPTION,
               task.STATUS,
               task.START_TIME,
               task.END_TIME,
               task.TYPE,
               task.TIME_TYPE,
               task.CREATED_BY,
               task.CURRENT_ASSIGNEE_USER_NAME,
               task.DELETED
        FROM TASK task
        WHERE 1=1
          AND task.TYPE = :type
        """);
    if (Objects.nonNull(parentTaskId)) {
      query.append("""
           AND task.ID IN (SELECT taskReference.CHILDREN_TASK_ID
                          FROM TASK_REFERENCE taskReference
                          WHERE taskReference.PARENT_TASK_ID = :parentTaskId)
          """, "parentTaskId", parentTaskId);
    }
    if (Objects.nonNull(type)) {
      query.append(" AND task.TYPE = :type", "type", type.name());
    }
    query.append(" ORDER BY task.START_TIME ASC");

    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), TaskResponse.class);
  }

  protected PrepareQuery buildQueryCursor(Long taskId, String createdDate) {
    if (Objects.isNull(taskId) || StringUtils.isBlank(createdDate)) {
      return null;
    }
    return new PrepareQuery("""
        AND (task.CREATED_DATE < :createDate
          OR (task.CREATED_DATE = :createDate AND task.ID < :taskId))
        """, Map.of("createDate", Timestamp.valueOf(createdDate),
        "taskId",
        taskId));
  }

  protected PrepareQuery buildSearchLike(String search) {
    if (StringUtils.isNotBlank(search)) {
      return new PrepareQuery(" AND LOWER(task.NAME) like :search", "search", search.toLowerCase());
    }
    return null;
  }

  protected PrepareQuery buildTypeIn(List<TaskTypeEnum> types) {
    if (CollectionUtils.isNotEmpty(types)) {
      return new PrepareQuery(" AND task.TYPE IN (:types)", "types",
          types.stream().map(TaskTypeEnum::toString).collect(
              Collectors.toList()));
    }
    return null;
  }

  protected PrepareQuery buildStatusIn(List<TaskStatusEnum> statuses) {
    if (CollectionUtils.isNotEmpty(statuses)) {
      return new PrepareQuery(" AND task.STATUS IN (:statuses)", "statuses",
          statuses.stream().map(TaskStatusEnum::toString).collect(
              Collectors.toList()));
    }
    return null;
  }

  protected PrepareQuery buildAssigneeUserIdIn(List<String> assigneeUserNames) {
    if (CollectionUtils.isNotEmpty(assigneeUserNames)) {
      return new PrepareQuery(" AND task.CURRENT_ASSIGNEE_USER_NAME IN (:assigneeUserNames)", "assigneeUserNames",
          assigneeUserNames);
    }
    return null;
  }

  protected PrepareQuery buildCreatorUserIdIn(List<String> creatorUserNames) {
    if (CollectionUtils.isNotEmpty(creatorUserNames)) {
      return new PrepareQuery(" AND task.CREATED_BY IN (:creatorUserNames)", "creatorUserNames",
          creatorUserNames);
    }
    return null;
  }

  protected PrepareQuery buildDateBetween(List<DateRangeRequest> dateRangeRequests) {
    List<DateRangeRequest> dateRanges = CollectionUtils.isNotEmpty(dateRangeRequests) ? dateRangeRequests.stream()
        .filter(ele -> StringUtils.isNotBlank(ele.getToDate()) && StringUtils.isNotBlank(ele.getFromDate())).toList() :
        List.of();
    if (CollectionUtils.isNotEmpty(dateRanges)) {
      var query = new PrepareQuery(" AND (");
      for (int i = 0; i < dateRanges.size(); i++) {
        var keyFromDate = "fromDate" + i;
        var keyToDate = "toDate" + i;
        var rangeQuery =
            new StringBuilder(" (NOT (task.END_TIME <:").append(keyFromDate).append(" OR task.START_TIME > :")
                .append(keyToDate).append(")) ");
        var dateRange = dateRanges.get(i);
        query.append(rangeQuery,
            Map.of(keyFromDate, DateUtils.convertStringToDate(dateRange.getFromDate()), keyToDate,
                DateUtils.convertStringToDate(dateRange.getToDate())));
        if (i != dateRanges.size() - 1) {
          query.append(" OR ");
        }
      }
      return query.append(") ");
    }
    return null;
  }

}