package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExecutionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.ExecutionRepositoryCustom;

/**
 * ExecutionRepositoryCustomImpl.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ExecutionRepositoryCustomImpl implements ExecutionRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public Page<ExecutionResponse> findAll(PaginationRequestDTO paginationRequest) {
    var query = new PrepareQuery("""
        SELECT execution.ID,
              execution.NAME,
              execution.DESCRIPTION,
              execution.TYPE,
              execution.DATABASE_CONNECTION_ID,
              execution.SCRIPT,
              execution.EXECUTION_GROUP_ID,
              executionGroup.NAME executionGroupName
        FROM EXECUTION execution
                LEFT JOIN EXECUTION_GROUP executionGroup
                          ON execution.EXECUTION_GROUP_ID = executionGroup.ID
        WHERE 1 = 1
        """
    ).append(buildQuerySearchLike(paginationRequest.getSearch()))
        .append(buildOrderQuery(paginationRequest));
    Pageable pageable = PageRequest.of(paginationRequest.getPage(),
        paginationRequest.getSize());
    return sqlQueryUtil.queryModel()
        .queryPaging(query.getQueryBuilder().toString(), query.getParams(),
            ExecutionResponse.class, pageable);
  }

  @Override
  public Page<ExecutionEntity> findAllWithPermission(PaginationRequestDTO page) {
    return null;
  }
  
  @Override
  public List<String> findFirstVariableNameInExecutionIdIgnoreCase(String executionId, Set<String> names) {
    var query = new PrepareQuery("""
          SELECT p.NAME
             FROM VARIABLE p
             WHERE p.EXECUTION_ID = :executionId
               AND LOWER(p.NAME) IN (:names)
             FETCH FIRST 1 ROWS ONLY
         """,
        Map.of("executionId", executionId, "names", names)
    );
    
    return sqlQueryUtil.queryModel().queryForList(query.getQuery(), query.getParams(), String.class);
  }
  
  protected PrepareQuery buildQuerySearchLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    return new PrepareQuery("AND (")
        .append(new PrepareQuery("LOWER(execution.NAME) LIKE :search"),
            LikeMatcher.CONTAINING)
        .append(
            new PrepareQuery(" OR LOWER(executionGroup.NAME) LIKE :search"),
            LikeMatcher.CONTAINING)
        .append(
            new PrepareQuery(" OR LOWER(execution.DESCRIPTION) LIKE :search",
                Map.of("search", search.toLowerCase())),
            LikeMatcher.CONTAINING)
        .append(")");
  }

  protected PrepareQuery buildOrderQuery(PaginationRequestDTO paginationRequest) {
    var sortBy = paginationRequest.getSortBy();
    PrepareQuery query;
    if (StringUtils.isBlank(sortBy)) {
      query = new PrepareQuery(" ORDER BY execution.CREATED_DATE ");
    } else {
      query = switch (sortBy) {
        case "name" -> new PrepareQuery(" ORDER BY execution.NAME ");
        case "description" -> new PrepareQuery(" ORDER BY execution.DESCRIPTION ");
        case "type" -> new PrepareQuery(" ORDER BY execution.TYPE ");
        case "executionGroupName" -> new PrepareQuery(" ORDER BY executionGroup.NAME ");
        default -> new PrepareQuery(" ORDER BY execution.CREATED_DATE ");
      };
    }
    return query.append(
        Objects.isNull(paginationRequest.getSortOrder()) ? null : paginationRequest.getSortOrder().name());
  }
}
