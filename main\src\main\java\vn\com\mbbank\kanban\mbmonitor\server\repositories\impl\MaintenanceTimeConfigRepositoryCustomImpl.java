package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import vn.com.mbbank.kanban.core.utils.KanbanEntityUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigDependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.MaintenanceTimePaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.MaintenanceTimeConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.MaintenanceTimeConfigRepositoryCustom;

/**
 * MaintenanceTimeConfigRepositoryCustomImpl.
 */
@RequiredArgsConstructor
public class MaintenanceTimeConfigRepositoryCustomImpl implements MaintenanceTimeConfigRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public Page<MaintenanceTimeConfigResponse> findAllBySearch(MaintenanceTimePaginationRequest request) {
    var query = new PrepareQuery("""
        SELECT maintenanceTimeConfig.ID AS id,
          maintenanceTimeConfig.NAME AS name,
          maintenanceTimeConfig.DESCRIPTION AS description,
          maintenanceTimeConfig.TYPE AS type,
          maintenanceTimeConfig.NEXT_TIME AS nextTime,
          maintenanceTimeConfig.UNIT AS unit,
          maintenanceTimeConfig.CRON_EXPRESSION AS cronExpression,
          maintenanceTimeConfig.START_TIME AS startTime,
          maintenanceTimeConfig.END_TIME AS endTime,
          maintenanceTimeConfig.ACTIVE AS active,
          maintenanceTimeConfig.RULE_GROUP AS ruleGroupColumn
        FROM MAINTENANCE_TIME_CONFIG maintenanceTimeConfig
        WHERE 1=1 
        """).append(buildQuerySearchLike(request.getSearch()));
    String sortColumn = KanbanEntityUtils.getColumnName(request.getSortBy(), MaintenanceTimeConfigEntity.class);
    if (!StringUtils.isNullOrEmpty(sortColumn)) {
      query.append(" ORDER BY maintenanceTimeConfig.").append(sortColumn);
      if (Objects.nonNull(request.getSortOrder())) {
        query.append(" ").append(request.getSortOrder().name()).append(", maintenanceTimeConfig.ID");
      }
    }
    return sqlQueryUtil.queryModel()
        .queryPaging(query.getQuery(), query.getParams(), MaintenanceTimeConfigResponse.class,
            PageRequest.of(request.getPage(), request.getSize()));
  }


  PrepareQuery buildQuerySearchLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    PrepareQuery prepareQuery = new PrepareQuery();
    prepareQuery.append(new PrepareQuery(" AND LOWER(maintenanceTimeConfig.NAME) LIKE :search "),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(maintenanceTimeConfig.TYPE) LIKE :search "),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(maintenanceTimeConfig.DESCRIPTION) LIKE :search ",
        Map.of("search", search.toLowerCase())), LikeMatcher.CONTAINING);
    return prepareQuery;
  }

  @Override
  public List<String> findDependencyNameByDependencyId(String dependencyId,
                                                       List<MaintenanceTimeConfigDependencyTypeEnum> type) {
    var query = new PrepareQuery("""
        SELECT maintenanceTimeConfig.name FROM MAINTENANCE_TIME_CONFIG maintenanceTimeConfig
        LEFT JOIN MAINTENANCE_TIME_CONFIG_DEPENDENCY maintenanceTimeConfigDependency
          ON maintenanceTimeConfig.ID = maintenanceTimeConfigDependency.MAINTENANCE_TIME_CONFIG_ID
        WHERE 1=1
        """);
    if (StringUtils.isNotBlank(dependencyId)) {
      query.append(" AND maintenanceTimeConfigDependency.DEPENDENCY_ID = :dependencyId", "dependencyId", dependencyId);
    }
    if (Objects.nonNull(type)) {
      query.append(" AND maintenanceTimeConfigDependency.TYPE IN (:type)", "type",
          type.stream().map(Enum::name).toList());
    }
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), String.class);
  }

}
