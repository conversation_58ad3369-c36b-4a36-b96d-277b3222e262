package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TaskUserEntity;

/**
 * Repository table  TaskUser.
 */
@Repository
public interface TaskUserRepository
    extends JpaCommonRepository<TaskUserEntity, Long>, TaskUserRepositoryCustom {

  /**
   * delete all user in a task.
   *
   * @param taskId list of taskId
   * @return number of record deleted
   */
  long deleteAllByTaskId(Long taskId);
}