package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.SysUserResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserEntity;

/**
 * Mapper logic SysUserResponse.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SysUserResponseMapper extends KanbanBaseMapper<SysUserResponse, SysUserEntity> {
  SysUserResponseMapper INSTANCE = Mappers.getMapper(SysUserResponseMapper.class);
}
