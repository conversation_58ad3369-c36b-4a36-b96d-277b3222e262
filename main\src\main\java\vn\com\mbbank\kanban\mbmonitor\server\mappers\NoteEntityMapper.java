package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NoteEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.NoteRequest;

/**
 * Mapper logic NoteEntityMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface NoteEntityMapper extends KanbanBaseMapper<NoteEntity, NoteRequest> {
  NoteEntityMapper INSTANCE = Mappers.getMapper(NoteEntityMapper.class);
}
