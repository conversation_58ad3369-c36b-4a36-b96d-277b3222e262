package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsConfigModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.TeamsConfigResponse;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/23/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TeamsConfigResponseToModelMapper  extends
    KanbanBaseMapper<TeamsConfigResponse, TeamsConfigModel> {
  TeamsConfigResponseToModelMapper INSTANCE = Mappers.getMapper(TeamsConfigResponseToModelMapper.class);
}
