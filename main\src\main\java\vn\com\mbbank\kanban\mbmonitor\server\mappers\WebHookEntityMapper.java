package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WebHookEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.WebHookDto;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 9/9/2024
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WebHookEntityMapper extends KanbanBaseMapper<WebHookEntity, WebHookDto> {
  WebHookEntityMapper INSTANCE = Mappers.getMapper(WebHookEntityMapper.class);
}
