package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TaskReferenceEntity;

/**
 * Repository table TaskUser.
 */
@Repository
public interface TaskReferenceRepository
    extends JpaCommonRepository<TaskReferenceEntity, Long> {

  /**
   * delete all taskReference by parentTaskId.
   *
   * @param parentTaskId shift handover task id
   * @return number of record deleted
   */
  long deleteAllByParentTaskId(Long parentTaskId);

  /**
   * delete all taskReference by childrenTaskId.
   *
   * @param childrenTaskId task id
   * @return number of record deleted
   */
  long deleteAllByChildrenTaskId(Long childrenTaskId);
}