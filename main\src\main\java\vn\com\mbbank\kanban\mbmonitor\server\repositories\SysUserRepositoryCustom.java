package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserEntity;

/**
 * Custom Repo table SysUser.
 */
public interface SysUserRepositoryCustom {
  /**
   * Find all user by roleId.
   *
   * @param roleId roleId
   * @param paging paging
   * @return list SysUserResponse
   */
  Page<SysUserEntity> findAllByRoleId(Long roleId, PaginationRequestDTO paging);

}
