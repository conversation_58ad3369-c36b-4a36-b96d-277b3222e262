package vn.com.mbbank.kanban.mbmonitor.server.services.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEncryptorUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.VariableRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.VariableResponse;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ErrorCode;
import vn.com.mbbank.kanban.mbmonitor.common.enums.VariableDataTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.VariableTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.GeneratorUtil;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.VariableRepository;

@ExtendWith(MockitoExtension.class)
class VariableServiceImplTest {

  @Mock
  private VariableRepository variableRepository;

  @InjectMocks
  private VariableServiceImpl variableService;

  private MockedStatic<GeneratorUtil> generatorUtilMockedStatic;
  private MockedStatic<KanbanEncryptorUtils> kanbanEncryptorUtilsMockedStatic;
  private MockedStatic<KanbanCommonUtil> kanbanCommonUtilMockedStatic;


  @BeforeEach
  void setUp() {
    generatorUtilMockedStatic = mockStatic(GeneratorUtil.class);
    kanbanEncryptorUtilsMockedStatic = mockStatic(KanbanEncryptorUtils.class);
    kanbanCommonUtilMockedStatic = mockStatic(KanbanCommonUtil.class);
  }

  @AfterEach
  void tearDown() {
    generatorUtilMockedStatic.close();
    kanbanEncryptorUtilsMockedStatic.close();
    kanbanCommonUtilMockedStatic.close();
  }

  @Test
  void findWithId_success_withFixedValue() throws BusinessException {
    String id = "testId";
    VariableEntity entity = new VariableEntity();
    entity.setId(id);
    entity.setName("TestVar");
    entity.setType(VariableTypeEnum.FIXED_VALUE);

    when(variableRepository.findById(id)).thenReturn(Optional.of(entity));

    List<VariableResponse> result = variableService.findWithId(id);

    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals(id, result.get(0).getId());
    verify(variableRepository, times(1)).findById(id);
  }

  @Test
  void findWithId_success_withDynamicValueJson() throws BusinessException {
    String id = "testId";
    String executionId = "execId";
    VariableEntity entity = new VariableEntity();
    entity.setId(id);
    entity.setName("TestVar");
    entity.setType(VariableTypeEnum.DYNAMIC_VALUE);
    entity.setDataType(VariableDataTypeEnum.JSON);
    entity.setExecutionId(executionId);

    VariableEntity entity2 = new VariableEntity();
    entity2.setId("testId2");
    entity2.setName("TestVar2");
    entity2.setExecutionId(executionId);

    List<VariableEntity> executionVariables = List.of(entity, entity2);

    when(variableRepository.findById(id)).thenReturn(Optional.of(entity));
    when(variableRepository.findAllByExecutionId(executionId)).thenReturn(executionVariables);

    List<VariableResponse> result = variableService.findWithId(id);

    assertNotNull(result);
    assertEquals(2, result.size());
    verify(variableRepository, times(1)).findById(id);
    verify(variableRepository, times(1)).findAllByExecutionId(executionId);
  }

  @Test
  void findWithId_success_withDynamicValueNonJson() throws BusinessException {
    String id = "testId";
    VariableEntity entity = new VariableEntity();
    entity.setId(id);
    entity.setName("TestVar");
    entity.setType(VariableTypeEnum.DYNAMIC_VALUE);
    entity.setDataType(VariableDataTypeEnum.RAW);

    when(variableRepository.findById(id)).thenReturn(Optional.of(entity));

    List<VariableResponse> result = variableService.findWithId(id);

    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals(id, result.get(0).getId());
    verify(variableRepository, times(1)).findById(id);
  }

  @Test
  void findWithId_notFound() {
    String id = "testId";
    when(variableRepository.findById(id)).thenReturn(Optional.empty());

    BusinessException exception = assertThrows(BusinessException.class, () -> variableService.findWithId(id));

    assertEquals(ErrorCode.VARIABLE_NOT_FOUND.getCode(), exception.getCode());
    verify(variableRepository, times(1)).findById(id);
  }

  @Test
  void findAll_success() {
    PaginationRequestDTO pageRequestDTO = new PaginationRequestDTO();
    pageRequestDTO.setPage(0);
    pageRequestDTO.setSize(10);
    PageRequest pageable = PageRequest.of(0, 10);
    
    VariableResponse entity = new VariableResponse();
    entity.setId("id1");
    Page<VariableResponse> page = new PageImpl<>(Collections.singletonList(entity), pageable, 1);

    when(variableRepository.findAll(pageRequestDTO)).thenReturn(page);

    Page<VariableResponse> result = variableService.findAll(pageRequestDTO);

    assertNotNull(result);
    assertEquals(1, result.getTotalElements());
    verify(variableRepository, times(1)).findAll(pageRequestDTO);
  }

  @Test
  void createOrUpdate_create_success() throws BusinessException {
    VariableRequest request = VariableRequest.builder()
        .name("NewVar").description("Desc").value("Value").hidden(false).build();

    when(variableRepository.existsByNameIgnoreCase(request.getName())).thenReturn(false);
    when(variableRepository.save(any(VariableEntity.class)))
        .thenAnswer(invocation -> invocation.getArgument(0));
    kanbanCommonUtilMockedStatic.when(() -> KanbanCommonUtil.isEmpty(request.getValue())).thenReturn(false);


    VariableEntity result = variableService.createOrUpdate(request);

    assertNotNull(result);
    assertEquals(request.getName(), result.getName());
    assertEquals(request.getValue(), result.getValue()); // Not hidden, so value should be plain
    verify(variableRepository, times(1)).existsByNameIgnoreCase(request.getName());
    verify(variableRepository, times(1)).save(any(VariableEntity.class));
    kanbanEncryptorUtilsMockedStatic.verify(() -> KanbanEncryptorUtils.encrypt(anyString()), never());
  }

  @Test
  void createOrUpdate_create_success_withHiddenValue() throws BusinessException {
    VariableRequest request = VariableRequest.builder()
        .name("NewHiddenVar").description("Desc").value("SecretValue").hidden(true).build();
    String encryptedValue = "encryptedSecretValue";

    kanbanEncryptorUtilsMockedStatic.when(() -> KanbanEncryptorUtils.encrypt(request.getValue())).thenReturn(encryptedValue);
    kanbanCommonUtilMockedStatic.when(() -> KanbanCommonUtil.isEmpty(request.getValue())).thenReturn(false);


    when(variableRepository.existsByNameIgnoreCase(request.getName())).thenReturn(false);
    when(variableRepository.save(any(VariableEntity.class)))
        .thenAnswer(invocation -> invocation.getArgument(0));

    VariableEntity result = variableService.createOrUpdate(request);

    assertNotNull(result);
    assertEquals(request.getName(), result.getName());
    assertEquals(encryptedValue, result.getValue());
    assertTrue(result.isHidden());
    verify(variableRepository, times(1)).existsByNameIgnoreCase(request.getName());
    verify(variableRepository, times(1)).save(any(VariableEntity.class));
  }
  
  @Test
  void createOrUpdate_create_success_withEmptyValueAndHidden() throws BusinessException {
    VariableRequest request = VariableRequest.builder()
        .name("NewVarEmptyHidden").description("Desc").value("adf").hidden(true).build();
    kanbanCommonUtilMockedStatic.when(() -> KanbanCommonUtil.isEmpty(request.getValue())).thenReturn(true);

    when(variableRepository.existsByNameIgnoreCase(request.getName())).thenReturn(false);
    when(variableRepository.save(any(VariableEntity.class)))
        .thenAnswer(invocation -> invocation.getArgument(0));

    VariableEntity result = variableService.createOrUpdate(request);

    assertNotNull(result);
    assertEquals(request.getName(), result.getName());
    assertTrue(result.isHidden());
    verify(variableRepository, times(1)).existsByNameIgnoreCase(request.getName());
    verify(variableRepository, times(1)).save(any(VariableEntity.class));
  }


  @Test
  void createOrUpdate_create_nameExisted() {
    VariableRequest request = VariableRequest.builder()
        .name("ExistingVar").description("Desc").value("Value").hidden(false).build();

    when(variableRepository.existsByNameIgnoreCase(request.getName())).thenReturn(true);

    BusinessException exception = assertThrows(BusinessException.class, () -> variableService.createOrUpdate(request));

    assertEquals(ErrorCode.VARIABLE_NAME_IS_EXISTED.getCode(), exception.getCode());
    verify(variableRepository, times(1)).existsByNameIgnoreCase(request.getName());
    verify(variableRepository, never()).save(any(VariableEntity.class));
  }

  @Test
  void createOrUpdate_update_success() throws BusinessException {
    String id = "existingId";
    VariableRequest request = VariableRequest.builder()
        .id(id).name("UpdatedVar").description("UpdatedDesc").value("UpdatedValue").hidden(false)
        .type(VariableTypeEnum.FIXED_VALUE).build();
    VariableEntity existingEntity = new VariableEntity();
    existingEntity.setId(id);
    existingEntity.setName("OldVar");
    existingEntity.setValue("OldValue");
    existingEntity.setType(VariableTypeEnum.FIXED_VALUE);

    when(variableRepository.findById(id)).thenReturn(Optional.of(existingEntity));
    when(variableRepository.existsByIdNotAndNameIgnoreCase(id, request.getName())).thenReturn(false);
    when(variableRepository.save(any(VariableEntity.class)))
        .thenAnswer(invocation -> invocation.getArgument(0));
    kanbanCommonUtilMockedStatic.when(() -> KanbanCommonUtil.isEmpty(request.getValue())).thenReturn(false);

    VariableEntity result = variableService.createOrUpdate(request);

    assertNotNull(result);
    assertEquals(id, result.getId());
    assertEquals(request.getName(), result.getName());
    assertEquals(request.getDescription(), result.getDescription());
    assertEquals(request.getValue(), result.getValue());
    assertFalse(result.isHidden());
    verify(variableRepository, times(1)).findById(id);
    verify(variableRepository, times(1)).existsByIdNotAndNameIgnoreCase(id, request.getName());
  }

  @Test
  void createOrUpdate_update_success_withHiddenValue() throws BusinessException {
    String id = "existingHiddenId";
    VariableRequest request = VariableRequest.builder()
      .id(id).name("UpdatedHiddenVar").description("Desc").value("NewSecretValue").hidden(true)
      .type(VariableTypeEnum.FIXED_VALUE).build();
    VariableEntity existingEntity = new VariableEntity();
    existingEntity.setId(id);
    existingEntity.setName("OldHiddenVar");
    existingEntity.setValue("OldEncryptedValue"); // Assume it was already encrypted or different
    existingEntity.setType(VariableTypeEnum.FIXED_VALUE); // Assume it was already encrypted or different
    existingEntity.setHidden(true);

    String encryptedValue = "encryptedNewSecretValue";

    when(variableRepository.findById(id)).thenReturn(Optional.of(existingEntity));
    when(variableRepository.existsByIdNotAndNameIgnoreCase(id, request.getName())).thenReturn(false);
    kanbanEncryptorUtilsMockedStatic.when(() -> KanbanEncryptorUtils.encrypt(request.getValue())).thenReturn(encryptedValue);
    kanbanCommonUtilMockedStatic.when(() -> KanbanCommonUtil.isEmpty(request.getValue())).thenReturn(false);

    when(variableRepository.save(any(VariableEntity.class)))
        .thenAnswer(invocation -> invocation.getArgument(0));

    VariableEntity result = variableService.createOrUpdate(request);

    assertNotNull(result);
    assertEquals(id, result.getId());
    assertEquals(request.getName(), result.getName());
    assertEquals(encryptedValue, result.getValue());
    assertTrue(result.isHidden());
    verify(variableRepository, times(1)).findById(id);
    verify(variableRepository, times(1)).existsByIdNotAndNameIgnoreCase(id, request.getName());
  }
  
  @Test
  void createOrUpdate_update_success_withEmptyValueAndHidden() throws BusinessException {
    String id = "existingEmptyHiddenId";
    VariableRequest request = VariableRequest.builder()
        .id(id).name("UpdatedEmptyHiddenVar").description("Desc").value("").hidden(true)
        .type(VariableTypeEnum.FIXED_VALUE).build();
    VariableEntity existingEntity = new VariableEntity();
    existingEntity.setId(id);
    existingEntity.setName("OldEmptyHiddenVar");
    existingEntity.setValue("SomeOldValue");
    existingEntity.setType(VariableTypeEnum.FIXED_VALUE);
    existingEntity.setHidden(false); // Test changing to hidden with empty value

    when(variableRepository.findById(id)).thenReturn(Optional.of(existingEntity));
    when(variableRepository.existsByIdNotAndNameIgnoreCase(id, request.getName())).thenReturn(false);
    kanbanCommonUtilMockedStatic.when(() -> KanbanCommonUtil.isEmpty(request.getValue())).thenReturn(true);
    
    when(variableRepository.save(any(VariableEntity.class)))
        .thenAnswer(invocation -> invocation.getArgument(0));

    VariableEntity result = variableService.createOrUpdate(request);

    assertNotNull(result);
    assertEquals(id, result.getId());
    assertEquals(request.getName(), result.getName());
    assertEquals("SomeOldValue", result.getValue()); // Value is empty, should not be encrypted
    assertTrue(result.isHidden());
    verify(variableRepository, times(1)).findById(id);
    verify(variableRepository, times(1)).existsByIdNotAndNameIgnoreCase(id, request.getName());
    kanbanEncryptorUtilsMockedStatic.verify(() -> KanbanEncryptorUtils.encrypt(anyString()), never());
  }


  @Test
  void createOrUpdate_update_notFound() {
    String id = "nonExistingId";
    VariableRequest request = VariableRequest.builder()
        .id(id).name("Var").value("Val").build();

    when(variableRepository.findById(id)).thenReturn(Optional.empty());

    BusinessException exception = assertThrows(BusinessException.class, () -> variableService.createOrUpdate(request));

    assertEquals(ErrorCode.VARIABLE_NOT_FOUND.getCode(), exception.getCode());
    verify(variableRepository, times(1)).findById(id);
    verify(variableRepository, never()).save(any(VariableEntity.class));
  }

  @Test
  void createOrUpdate_update_nameExisted() {
    String id = "idToUpdate";
    VariableRequest request = VariableRequest.builder()
        .id(id).name("NameThatExists").value("Value").type(VariableTypeEnum.FIXED_VALUE).build();
    VariableEntity existingEntity = new VariableEntity(); // This is the entity being updated
    existingEntity.setId(id);
    existingEntity.setType(VariableTypeEnum.FIXED_VALUE);
    
    when(variableRepository.findById(id)).thenReturn(Optional.of(existingEntity));
    when(variableRepository.existsByIdNotAndNameIgnoreCase(id, request.getName())).thenReturn(true);

    BusinessException exception = assertThrows(BusinessException.class, () -> variableService.createOrUpdate(request));

    assertEquals(ErrorCode.VARIABLE_NAME_IS_EXISTED.getCode(), exception.getCode());
    verify(variableRepository, never()).save(any(VariableEntity.class));
  }

  @Test
  void deleteWithId_success() throws BusinessException {
    String id = "idToDelete";
    VariableEntity entity = new VariableEntity();
    entity.setId(id);

    when(variableRepository.findById(id)).thenReturn(Optional.of(entity));
    doNothing().when(variableRepository).delete(entity); // Since delete is from BaseServiceImpl

    variableService.deleteWithId(id);

    verify(variableRepository, times(1)).findById(id);
    verify(variableRepository, times(1)).delete(entity);
  }

  @Test
  void deleteWithId_notFound() {
    String id = "nonExistingIdToDelete";
    when(variableRepository.findById(id)).thenReturn(Optional.empty());

    BusinessException exception = assertThrows(BusinessException.class, () -> variableService.deleteWithId(id));

    assertEquals(ErrorCode.VARIABLE_NOT_FOUND.getCode(), exception.getCode());
    verify(variableRepository, times(1)).findById(id);
    verify(variableRepository, never()).delete(any(VariableEntity.class));
  }

  @Test
  void findAllByNameIn_success() {
    List<String> names = List.of("Var1", "Var2");
    VariableEntity entity1 = new VariableEntity();
    entity1.setName("Var1");
    VariableEntity entity2 = new VariableEntity();
    entity2.setName("Var2");
    List<VariableEntity> entities = List.of(entity1, entity2);

    when(variableRepository.findAllByNameIn(names)).thenReturn(entities);

    List<VariableEntity> result = variableService.findAllByNameIn(names);

    assertNotNull(result);
    assertEquals(2, result.size());
    verify(variableRepository, times(1)).findAllByNameIn(names);
  }

  @Test
  void findAllByNameIn_emptyListInput() {
    List<String> names = Collections.emptyList();
    when(variableRepository.findAllByNameIn(names)).thenReturn(Collections.emptyList());

    List<VariableEntity> result = variableService.findAllByNameIn(names);

    assertNotNull(result);
    assertTrue(result.isEmpty());
    verify(variableRepository, times(1)).findAllByNameIn(names);
  }

  @Test
  void findAllByNameIn_noMatch() {
    List<String> names = List.of("NonExistentVar");
    when(variableRepository.findAllByNameIn(names)).thenReturn(Collections.emptyList());

    List<VariableEntity> result = variableService.findAllByNameIn(names);

    assertNotNull(result);
    assertTrue(result.isEmpty());
    verify(variableRepository, times(1)).findAllByNameIn(names);
  }
}
