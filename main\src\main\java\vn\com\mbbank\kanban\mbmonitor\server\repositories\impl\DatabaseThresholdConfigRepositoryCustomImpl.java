package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.Map;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEntityUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.DatabaseThresholdConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.DatabaseThresholdConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.DatabaseThresholdConfigRepositoryCustom;

/**
 * Implement DatabaseThresholdConfigRepositoryCustom table DatabaseThresholdConfig.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DatabaseThresholdConfigRepositoryCustomImpl implements DatabaseThresholdConfigRepositoryCustom {
  SqlQueryUtil sqlQueryUtil;


  @Override
  public DatabaseThresholdConfigResponse findWithId(String id) {
    var query = new PrepareQuery(
        """
            SELECT databaseThresholdConfig.ID,
                   databaseThresholdConfig.NAME,
                   databaseThresholdConfig.DESCRIPTION,
                   databaseThresholdConfig.SQL_COMMAND,
                   databaseThresholdConfig.CONTENT,
                   databaseThresholdConfig.CONTENT_JSON,
                   databaseThresholdConfig.RECIPIENT,
                    databaseThresholdConfig.ACTIVE,
                   databaseThresholdConfig.PRIORITY_ID,
                   databaseThresholdConfig.CRON_TIME,
                   databaseThresholdConfig.CONDITION_VALUE,
                   databaseThresholdConfig.CONDITION_OPERATOR,
                   databaseThresholdConfig.DATABASE_CONNECTION_ID,
                   databaseThresholdConfig.PRIORITY_ID AS priorityConfigId,
                   databaseConnection.NAME as databaseConnectionName,
                   databaseThresholdConfig.APPLICATION_ID,
                   databaseThresholdConfig.SERVICE_ID,
                   application.NAME AS applicationName,
                   service.NAME AS serviceName
            FROM DATABASE_THRESHOLD_CONFIG databaseThresholdConfig
            JOIN DATABASE_CONNECTION databaseConnection
            ON databaseConnection.ID = databaseThresholdConfig.DATABASE_CONNECTION_ID
            JOIN APPLICATION application ON application.ID= databaseThresholdConfig.APPLICATION_ID
            JOIN SERVICE service ON service.ID= databaseThresholdConfig.SERVICE_ID
            """
    ).append("WHERE databaseThresholdConfig.ID= :id", "id", id);
    return sqlQueryUtil.queryModel()
        .queryForObject(query.getQuery(), query.getParams(), DatabaseThresholdConfigResponse.class);
  }

  @Override
  public Page<DatabaseThresholdConfigResponse> findAll(PaginationRequestDTO paginationRequest) {
    var query = new PrepareQuery("""
        SELECT databaseThresholdConfig.ID AS id, databaseThresholdConfig.NAME AS name, databaseConnection.NAME
         AS databaseConnectionName, databaseThresholdConfig.DESCRIPTION AS description,
         databaseThresholdConfig.CONTENT_JSON AS contentJson, databaseThresholdConfig.RECIPIENT AS recipient,
          databaseThresholdConfig.ACTIVE AS active, databaseThresholdConfig.CRON_TIME AS cronTime,
          service.NAME AS serviceName, application.NAME AS applicationName,
           alertPriorityConfig.NAME AS priorityConfigName
        FROM DATABASE_THRESHOLD_CONFIG databaseThresholdConfig
        JOIN DATABASE_CONNECTION databaseConnection
        ON databaseConnection.ID = databaseThresholdConfig.DATABASE_CONNECTION_ID
        JOIN SERVICE service
        ON service.ID = databaseThresholdConfig.SERVICE_ID
        JOIN APPLICATION application
        ON application.ID = databaseThresholdConfig.APPLICATION_ID
        JOIN ALERT_PRIORITY_CONFIG alertPriorityConfig
        ON alertPriorityConfig.ID = databaseThresholdConfig.PRIORITY_ID
        WHERE 1=1
        """
    ).append(buildQuerySearchLike(paginationRequest.getSearch()));

    String sortColumn = KanbanEntityUtils.getColumnName(paginationRequest.getSortBy(),
        DatabaseThresholdConfigEntity.class);
    if (!KanbanCommonUtil.isEmpty(sortColumn)) {
      query.append(" ORDER BY databaseThresholdConfig.").append(sortColumn);
    } else if ("databaseConnectionName".equals(paginationRequest.getSortBy())) {
      query.append(" ORDER BY databaseConnection.NAME");
    } else if ("serviceName".equals(paginationRequest.getSortBy())) {
      query.append(" ORDER BY service.NAME");
    } else if ("applicationName".equals(paginationRequest.getSortBy())) {
      query.append(" ORDER BY application.NAME");
    } else if ("priorityConfigName".equals(paginationRequest.getSortBy())) {
      query.append(" ORDER BY alertPriorityConfig.NAME");
    }
    if (paginationRequest.getSortOrder() != null) {
      query.append(" ").append(paginationRequest.getSortOrder().name());
    }
    Pageable pageable = PageRequest.of(paginationRequest.getPage(),
        paginationRequest.getSize());
    return sqlQueryUtil.queryModel()
        .queryPaging(query.getQueryBuilder().toString(), query.getParams(),
            DatabaseThresholdConfigResponse.class, pageable);

  }

  PrepareQuery buildQuerySearchLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    PrepareQuery prepareQuery = new PrepareQuery();
    prepareQuery.append(new PrepareQuery("AND (LOWER(databaseThresholdConfig.NAME) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(databaseThresholdConfig.DESCRIPTION) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(databaseThresholdConfig.CRON_TIME) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(service.NAME) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(application.NAME) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(alertPriorityConfig.NAME) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(databaseThresholdConfig.RECIPIENT) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(databaseThresholdConfig.CONTENT) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(databaseConnection.NAME) LIKE :search",
        Map.of("search", search.toLowerCase())), LikeMatcher.CONTAINING);
    prepareQuery.append(" )");
    return prepareQuery;
  }

}
