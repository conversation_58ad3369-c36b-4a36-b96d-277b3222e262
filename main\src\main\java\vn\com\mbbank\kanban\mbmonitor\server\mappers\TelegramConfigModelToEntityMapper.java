package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TelegramConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.TelegramConfigModel;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 3/27/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TelegramConfigModelToEntityMapper extends KanbanBaseMapper<TelegramConfigModel, TelegramConfigEntity> {
  TelegramConfigModelToEntityMapper INSTANCE = Mappers.getMapper(TelegramConfigModelToEntityMapper.class);
}
