package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.sql.Timestamp;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionHistoryResponse;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.ExecutionHistoryCursorModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.ExecutionHistorySearchRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.ExecutionHistoryRepositoryCustom;

/**
 * ExecutionHistoryRepositoryCustomImpl.
 */
@RequiredArgsConstructor
public class ExecutionHistoryRepositoryCustomImpl implements ExecutionHistoryRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public CursorPageResponse<ExecutionHistoryResponse, ExecutionHistoryCursorModel> findAll(
      ExecutionHistorySearchRequest request, String executeBy) {
    var query = new PrepareQuery("""
         SELECT executionHistory.ID,
                executionHistory.EXECUTION_NAME,
                executionHistory.EXECUTION_TYPE,
                executionHistory.STATUS,
                executionHistory.EXECUTION_BY,
                executionHistory.START_TIME,
                executionHistory.END_TIME
        FROM EXECUTION_HISTORY executionHistory
        WHERE 1=1
        """)
        .append(buildExecuteByEqual(executeBy))
        .append(buildQueryCursor(request.getId(), request.getStartTime()))
        .append(" ORDER BY executionHistory.START_TIME DESC, executionHistory.ID DESC")
        .append(" FETCH FIRST " + (request.getPageSize() + 1) + " ROWS ONLY ");

    //Get 1 element left to see if there is a next page.
    var result = sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), ExecutionHistoryResponse.class);
    if (result.size() > request.getPageSize()) {
      return new CursorPageResponse<>(result.subList(0, request.getPageSize()),
          new ExecutionHistoryCursorModel(result.get(result.size() - 2).getId(),
              result.get(result.size() - 2).getStartTime())
      );
    }
    return new CursorPageResponse<>(result, null);
  }


  protected PrepareQuery buildQueryCursor(String executionHistoryId, String startTime) {
    if (Objects.isNull(executionHistoryId) || StringUtils.isBlank(startTime)) {
      return null;
    }
    return new PrepareQuery("""
        AND (executionHistory.START_TIME < :startTime
          OR (executionHistory.START_TIME = :startTime AND executionHistory.ID < :executionHistoryId))
        """, Map.of("startTime", Timestamp.valueOf(startTime),
        "executionHistoryId",
        executionHistoryId));
  }

  protected PrepareQuery buildExecuteByEqual(String executeBy) {
    if (StringUtils.isBlank(executeBy)) {
      return null;
    }
    return new PrepareQuery(" AND executionHistory.EXECUTION_BY = :executeBy",
        "executeBy", executeBy);
  }

}