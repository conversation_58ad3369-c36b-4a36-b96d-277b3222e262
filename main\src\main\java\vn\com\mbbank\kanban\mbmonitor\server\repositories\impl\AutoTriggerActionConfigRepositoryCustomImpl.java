package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.enums.DependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AutoTriggerActionConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.AutoTriggerActionConfigRepositoryCustom;

/**
 * AutoTriggerActionConfigRepositoryCustomImpl.
 */
@RequiredArgsConstructor
public class AutoTriggerActionConfigRepositoryCustomImpl implements AutoTriggerActionConfigRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public List<String> findDependencyNameByDependencyId(String dependencyId,
                                                       List<DependencyTypeEnum> type) {
    var query = new PrepareQuery("""
        SELECT autoTriggerActionConfig.name FROM AUTO_TRIGGER_ACTION_CONFIG autoTriggerActionConfig
        LEFT JOIN AUTO_TRIGGER_ACTION_CONFIG_DEPENDENCY autoTriggerActionConfigDependency
          ON autoTriggerActionConfig.ID = autoTriggerActionConfigDependency.AUTO_TRIGGER_ACTION_CONFIG_ID
        WHERE 1=1
        """);
    if (StringUtils.isNotBlank(dependencyId)) {
      query.append(" AND autoTriggerActionConfigDependency.DEPENDENCY_ID = :dependencyId", "dependencyId",
          dependencyId);
    }
    if (Objects.nonNull(type)) {
      query.append(" AND autoTriggerActionConfigDependency.TYPE IN (:type)", "type",
          type.stream().map(Enum::name).toList());
    }
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), String.class);
  }

  @Override
  public Page<AutoTriggerActionConfigResponse> findAllBySearch(PaginationRequestDTO request) {
    var query = new PrepareQuery("""
            SELECT autoTriggerActionConfig.ID AS id,
            autoTriggerActionConfig.NAME AS name,
            autoTriggerActionConfig.DESCRIPTION AS description,
            autoTriggerActionConfig.ACTIVE AS active,
            autoTriggerActionConfig.RULE_GROUP AS ruleGroupColumn,
            autoTriggerActionConfig.CREATED_DATE AS createdDate,
            autoTriggerActionConfig.TIME_SINCE_LAST_TRIGGER AS timeSinceLastTrigger
            FROM AUTO_TRIGGER_ACTION_CONFIG autoTriggerActionConfig
            WHERE 1=1
            """);
    if (!StringUtils.isBlank(request.getSearch())) {
      query.append(""" 
              AND autoTriggerActionConfig.id IN (
              SELECT DISTINCT autoTriggerActionConfig.id  FROM AUTO_TRIGGER_ACTION_CONFIG autoTriggerActionConfig
              LEFT JOIN AUTO_TRIGGER_ACTION_CONFIG_EXECUTION_MAPPER autoTriggerActionConfigExecutionMap
              ON autoTriggerActionConfigExecutionMap.AUTO_TRIGGER_ACTION_CONFIG_ID = autoTriggerActionConfig.ID
              LEFT JOIN EXECUTION execution ON execution.ID = autoTriggerActionConfigExecutionMap.EXECUTION_ID 
              WHERE 1=1 
              """);
    }
    query.append(buildQuerySearchLike(request.getSearch()))
            .append(buildOrderQuery(request));
    return sqlQueryUtil.queryModel()
            .queryPaging(query.getQuery(), query.getParams(), AutoTriggerActionConfigResponse.class,
                    PageRequest.of(request.getPage(), request.getSize()));
  }

  protected PrepareQuery buildQuerySearchLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    return new PrepareQuery("AND (")
            .append(new PrepareQuery("LOWER(autoTriggerActionConfig.NAME) LIKE :search"),
                    LikeMatcher.CONTAINING)
            .append(
                    new PrepareQuery(" OR LOWER(autoTriggerActionConfig.DESCRIPTION) LIKE :search",
                            Map.of("search", search.toLowerCase())),
                    LikeMatcher.CONTAINING)
            .append(
                    new PrepareQuery(" OR LOWER(autoTriggerActionConfig.RULE_GROUP) LIKE :search",
                            Map.of("search", search.toLowerCase())),
                    LikeMatcher.CONTAINING)
            .append(
                    new PrepareQuery(" OR LOWER(execution.NAME) LIKE :search",
                            Map.of("search", search.toLowerCase())),
                    LikeMatcher.CONTAINING)
            .append("))")
            .append("");
  }

  protected PrepareQuery buildOrderQuery(PaginationRequestDTO paginationRequest) {
    var sortBy = paginationRequest.getSortBy();
    PrepareQuery query;
    if (StringUtils.isBlank(sortBy)) {
      query = new PrepareQuery(" ORDER BY autoTriggerActionConfig.CREATED_DATE ");
    } else {
      query = switch (sortBy) {
        case "name" -> new PrepareQuery(" ORDER BY autoTriggerActionConfig.NAME ");
        case "description" -> new PrepareQuery(" ORDER BY autoTriggerActionConfig.DESCRIPTION ");
        default -> new PrepareQuery(" ORDER BY autoTriggerActionConfig.CREATED_DATE ");
      };
    }
    return query.append(
            Objects.isNull(paginationRequest.getSortOrder()) ? null : paginationRequest.getSortOrder().name());
  }
}
