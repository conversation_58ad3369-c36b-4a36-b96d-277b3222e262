package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.telegram.TelegramAlertConfigModel;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/7/2025
 */
public interface TelegramAlertConfigRepositoryCustom {
  /**
   * Find all service config telegram.
   *
   * @param page page
   * @return services
   */
  Page<TelegramAlertConfigModel> findServicesWithPage(PaginationRequestDTO page);

  /**
   * Find all applications config telegram by service.
   *
   * @param serviceId serviceId
   * @param page page
   * @return applications
   */
  Page<TelegramAlertConfigModel> findApplicationByServiceIdWithPage(String serviceId,
                                                                    PaginationRequestDTO page);
}
