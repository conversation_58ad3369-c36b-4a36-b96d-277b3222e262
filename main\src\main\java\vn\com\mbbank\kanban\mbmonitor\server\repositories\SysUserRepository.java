package vn.com.mbbank.kanban.mbmonitor.server.repositories;


import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserEntity;

/**
 * Repository table  SysUser.
 */
@Repository
public interface SysUserRepository
    extends JpaCommonRepository<SysUserEntity, Long>, SysUserRepositoryCustom {
  /**
   * Find user by userName.
   *
   * @param username username
   * @return SysUserEntity
   */
  Optional<SysUserEntity> findByUserName(String username);

  /**
   * Find All User by list username.
   *
   * @param usernames list username
   * @return list user
   */
  List<SysUserEntity> findAllByUserNameIn(List<String> usernames);

  /**
   * Find all user has active.
   *
   * @param isActive true:active false: inactive
   * @return List SysUserEntity
   */
  List<SysUserEntity> findAllByIsActive(Boolean isActive);

  /**
   * Active list user.
   *
   * @param userIds list userIds.
   * @return total row update
   */
  @Modifying
  @Query("UPDATE  SysUserEntity e SET e.isActive = true WHERE e.id IN :userIds")
  int activeUserByUserIdsIn(List<Long> userIds);

  /**
   * InActive list user.
   *
   * @param userIds list userIds.
   * @return total row update
   */
  @Modifying
  @Query("UPDATE  SysUserEntity e SET e.isActive = false WHERE e.id IN :userIds")
  int inActiveyUserIdsIn(List<Long> userIds);

  /**
   * check exist name.
   *
   * @param userName userName
   * @return boolean
   */
  boolean existsByUserName(String userName);
}
