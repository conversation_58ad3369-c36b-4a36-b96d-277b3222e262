package vn.com.mbbank.kanban.mbmonitor.server.dtos.response;

import java.util.ArrayList;
import java.util.List;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupOutputEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;

/**
 * AlertGroupConfigResponse.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AlertGroupConfigResponse {
  Long id;
  String name;
  String description;
  AlertGroupOutputEnum alertOutput;
  Boolean deleted;
  Integer position;
  Boolean active;
  AlertGroupConfigTypeEnum type;
  @Builder.Default
  List<RuleGroupType> ruleGroups = new ArrayList<>();
  @Builder.Default
  List<Long> customObjectIds = new ArrayList<>();
  @Builder.Default
  List<ServiceResponse> services = new ArrayList<>();
  @Builder.Default
  List<ApplicationResponse> applications = new ArrayList<>();
  private ServiceResponse customService;
  private ApplicationResponse customApplication;
  private String customContent;
  private String customRecipient;
  private Long customPriorityConfigId;
}
