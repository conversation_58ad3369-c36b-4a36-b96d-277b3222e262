package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.List;
import java.util.Map;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertRequestStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.AlertRequestDatabaseRepositoryCustom;

/**
 * Implement AlertRequestDatabaseRepositoryCustom table AlertRequestDatabase.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AlertRequestDatabaseRepositoryCustomImpl implements AlertRequestDatabaseRepositoryCustom {
  SqlQueryUtil sqlQueryUtil;


  @Override
  public List<String> findByDatabaseConnectionIdAndStatusIsNot(
      Long id, AlertRequestStatusEnum status
  ) {
    String sql = """
        SELECT alertRequest.ID
        FROM ALERT_REQUEST_DATABASE alertRequestDatabase
        JOIN ALERT_REQUEST alertRequest
            ON alertRequestDatabase.ALERT_REQUEST_ID = alertRequest.ID
        WHERE alertRequestDatabase.DATABASE_CONNECTION_ID = :id
          AND alertRequest.STATUS <> :status
        """;

    return sqlQueryUtil.queryModel()
        .queryForList(sql, Map.of("id", id, "status", status.name()), String.class);
  }

}
