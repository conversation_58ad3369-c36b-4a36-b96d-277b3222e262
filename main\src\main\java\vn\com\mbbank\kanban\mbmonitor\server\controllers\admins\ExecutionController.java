package vn.com.mbbank.kanban.mbmonitor.server.controllers.admins;

import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import vn.com.mbbank.kanban.core.common.ResponseData;
import vn.com.mbbank.kanban.core.exceptions.BusinessException;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.AclPermission;
import vn.com.mbbank.kanban.mbmonitor.common.annotations.HasPermission;
import vn.com.mbbank.kanban.mbmonitor.common.controller.BaseController;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.PaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecuteScriptRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.execution.ExecutionRequest;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecuteScriptResponse;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.execution.ExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ExecutionTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.common.integrations.inbounds.urls.ServerUrl;
import vn.com.mbbank.kanban.mbmonitor.common.utils.ResponseUtils;
import vn.com.mbbank.kanban.mbmonitor.server.mappers.ExecutionResponseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionDependencyService;
import vn.com.mbbank.kanban.mbmonitor.server.services.ExecutionService;

/**
 * ExecutionController.
 */
@RestController
@RequestMapping(ServerUrl.EXECUTION_URL)
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ExecutionController extends BaseController {

  private final ExecutionService executionService;
  private final ExecutionDependencyService executionDependencyService;
  private final ExecutionResponseMapper executionResponseMapper = ExecutionResponseMapper.INSTANCE;

  /**
   * Api find config by ID.
   *
   * @param id id of config.
   * @return ExecutionResponse.
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EXECUTION, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.AUTO_TRIGGER_ACTION_CONFIG, action = PermissionActionEnum.VIEW),
  })
  @GetMapping(value = "/{id}")
  ResponseData<ExecutionResponse> findById(@PathVariable String id)
      throws BusinessException {
    return ResponseUtils.success((executionService.findWithId(id)));
  }

  /**
   * Execute script.
   *
   * @param request executionRunningRequest.
   * @return ExecutionResponse.
   */
  @PostMapping(value = "/execute")
  ResponseData<ExecuteScriptResponse> execute(@RequestBody @Valid ExecuteScriptRequest request)
      throws BusinessException {
    return ResponseUtils.success(executionService.execute(request));
  }

  /**
   * Api find config by ID.
   *
   * @param id id of config.
   * @return ExecutionResponse.
   */
  @GetMapping(value = "/{id}/with-envs")
  ResponseData<ExecutionResponse> findByIdWithVariable(@PathVariable String id)
      throws BusinessException {
    return ResponseUtils.success(executionService.findByIdWithVariable(id));
  }

  /**
   * Api find a list of config.
   *
   * @param request search
   * @return List of ExecutionResponse
   */
  @GetMapping
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EXECUTION, action = PermissionActionEnum.VIEW)
  })
  ResponseData<Page<ExecutionResponse>> findAllWithPaging(@ModelAttribute PaginationRequest request) {
    return ResponseUtils.success(executionService.findAll(request));
  }

  /**
   * Api save config.
   *
   * @param request input data.
   * @return ExecutionResponse.
   * @throws BusinessException exception
   */
  @PostMapping
  ResponseData<ExecutionResponse> save(
      @Valid @RequestBody ExecutionRequest request) throws BusinessException {
    makeSureCreateOrUpdate(PermissionModuleEnum.EXECUTION, PermissionActionEnum.CREATE,
        PermissionActionEnum.EDIT, Collections.singleton(request.getId()));
    return ResponseUtils.success(executionResponseMapper.map(executionService.createOrUpdate(request)));
  }

  /**
   * Api delete config.
   *
   * @param id config id.
   * @return String
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EXECUTION, action = PermissionActionEnum.DELETE)
  })
  @DeleteMapping(value = "/{id}")
  ResponseData<String> deleteById(@PathVariable String id) throws BusinessException {
    executionService.deleteWithId(id);
    return ResponseUtils.success("OK");
  }

  /**
   * Find all execution by executionGroupId.
   *
   * @param executionGroupId ID group.
   * @return Response containing a list of ExecutionResponse.
   */
  @HasPermission(value = {
      @AclPermission(module = PermissionModuleEnum.EXECUTION_GROUP, action = PermissionActionEnum.DELETE),
      @AclPermission(module = PermissionModuleEnum.USER_MANAGEMENT, action = PermissionActionEnum.VIEW),
      @AclPermission(module = PermissionModuleEnum.ROLE_MANAGEMENT, action = PermissionActionEnum.VIEW),
  })
  @GetMapping(params = "executionGroupId")
  public ResponseData<List<ExecutionResponse>> findAllByExecutionGroupId(
      @RequestParam String executionGroupId) {
    return ResponseUtils.success(
       executionService.findAllByExecutionGroupId(executionGroupId));
  }

  /**
   * Find all execution by executionGroupId.
   *
   * @param executionGroupId ID group.
   * @return Response containing a list of ExecutionResponse.
   */
  @GetMapping(value = "/with-permissions", params = "executionGroupId")
  public ResponseData<List<ExecutionResponse>> findAllWithPermissionByExecutionGroupId(
      @RequestParam String executionGroupId)
      throws BusinessException {
    return ResponseUtils.success(executionResponseMapper.map(
        executionDependencyService.findAllWithPermissionByExecutionGroupId(executionGroupId)));
  }

  /**
   * find execution by type.
   *
   * @param type to find
   * @return List ExecutionResponse
   */
  @GetMapping(value = "/with-type", params = "type")
  public ResponseData<List<ExecutionResponse>> findAllWithType(@RequestParam ExecutionTypeEnum type) {
    return ResponseUtils.success(executionResponseMapper.map(
            executionService.findAllByType(type)));
  }
  
  /**
   * find execution by type.
   *
   * @param type to find
   * @return List ExecutionResponse
   */
  @GetMapping(value = "/with-type-not", params = "type")
  public ResponseData<List<ExecutionResponse>> findAllByTypeNot(@RequestParam ExecutionTypeEnum type) {
    return ResponseUtils.success(executionResponseMapper.map(
      executionService.findAllByTypeNot(type)));
  }
}
