package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AutoTriggerActionConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AutoTriggerActionConfigRequest;

/**
 * Mapper interface for mapping between `AutoTriggerActionConfigEntity` and `AutoTriggerActionConfigRequest`.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AutoTriggerActionConfigEntityMapper extends
    KanbanBaseMapper<AutoTriggerActionConfigEntity, AutoTriggerActionConfigRequest> {
  AutoTriggerActionConfigEntityMapper INSTANCE = Mappers.getMapper(AutoTriggerActionConfigEntityMapper.class);

  /**
   * map from AutoTriggerActionConfigRequest to AutoTriggerActionConfigEntity.
   *
   * @param entity  AutoTriggerActionConfigEntity.
   * @param request AutoTriggerActionConfigRequest.
   */
  void merge(@MappingTarget AutoTriggerActionConfigEntity entity, AutoTriggerActionConfigRequest request);
}
