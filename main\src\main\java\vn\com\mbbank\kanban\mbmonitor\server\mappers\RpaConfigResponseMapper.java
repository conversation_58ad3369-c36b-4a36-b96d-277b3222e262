package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.RpaConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.RpaConfigResponse;

/**
 * Mapper logic RpaConfigResponseMapper.
 *
 * <AUTHOR>
 * @created_date 29/5/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface RpaConfigResponseMapper extends KanbanBaseMapper<RpaConfigResponse, RpaConfigEntity> {
  RpaConfigResponseMapper INSTANCE = Mappers.getMapper(RpaConfigResponseMapper.class);
}
