package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.query.EscapeCharacter;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEntityUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserEntity;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.SysUserRepositoryCustom;

/**
 * Implement SysUserRepositoryCustom table SYS_USER.
 */
@RequiredArgsConstructor
public class SysUserRepositoryCustomImpl implements SysUserRepositoryCustom {
  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public Page<SysUserEntity> findAllByRoleId(Long roleId, PaginationRequestDTO paging) {

    String query = """
          SELECT
             sysUser.*
          FROM
              SYS_USER sysUser
          LEFT JOIN SYS_USER_ROLE sysUserRole ON
              sysUser.ID = sysUserRole.USER_ID
          WHERE
             sysUserRole.ROLE_ID = :roleId
        """;
    StringBuilder queryBuilder = new StringBuilder(query);
    if (!KanbanCommonUtil.isEmpty(paging.getSearch())) {
      queryBuilder.append(" AND LOWER(sysUser.USERNAME) like :search ");
    }

    queryBuilder.append("ORDER BY sysUser.")
        .append(KanbanEntityUtils.getColumnName(paging.getSortBy(), SysUserEntity.class))
        .append(" ").append(paging.getSortOrder());

    Pageable pageable = PageRequest.of(paging.getPage(), paging.getSize());
    return sqlQueryUtil.queryModel()
        .queryPaging(queryBuilder.toString(), Map.of("roleId", roleId, "search",
                "%" + EscapeCharacter.DEFAULT.escape(paging.getSearch().toLowerCase())
                    +
                    "% escape '\\'"),
            SysUserEntity.class, pageable);
  }
}
