package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.WebHookEntity;

/**
 * WebHook Repository.
 *
 * <AUTHOR>
 * @created_date 8/19/2024
 */
@Repository
public interface WebHookRepository extends JpaCommonRepository<WebHookEntity, Long> {
  /**
   * find config webhook by token.
   *
   * @param token token
   * @return config
   */
  Optional<WebHookEntity> findFirstByToken(String token);

  /**
   * Check exist name.
   *
   * @param name name
   * @return true/false
   */
  boolean existsWebHookEntitiesByNameIgnoreCase(String name);

  /**
   * find by serviceId.
   *
   * @param serviceId serviceId
   * @return list webhook config
   */
  List<WebHookEntity> findAllByServiceId(String serviceId);

  /**
   * find all webhook by applicationId.
   *
   * @param applicationId applicationId
   * @return list webhook config
   */
  List<WebHookEntity> findAllByApplicationId(String applicationId);


  /**
   * find all webhook by priorityId.
   *
   * @param priorityId id of priority.
   * @return list webhook config
   */
  List<WebHookEntity> findAllByAlertPriorityConfigId(Long priorityId);

  /**
   * Counts the number of webhook objects with id of service.
   *
   * @param serviceId id of service.
   * @return The number of webhook objects with id of service.
   */

  long countByServiceId(String serviceId);

  /**
   * Counts the number of webhook objects with id of application.
   *
   * @param applicationId id of application.
   * @return The number of webhook objects with id of application.
   */

  long countByApplicationId(String applicationId);
}
