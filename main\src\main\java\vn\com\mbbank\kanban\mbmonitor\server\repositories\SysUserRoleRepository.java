package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserRoleEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 9/23/2024
 */
@Repository
public interface SysUserRoleRepository extends JpaCommonRepository<SysUserRoleEntity, Long> {
  /**
   * Find All by list userId.
   *
   * @param userIds userIds
   * @return list SysUserRoleEntity
   */
  List<SysUserRoleEntity> findAllByUserIdIn(List<Long> userIds);

  /**
   * Find  by userId.
   *
   * @param userId userId
   * @return list SysUserRoleEntity
   */
  List<SysUserRoleEntity> findAllByUserId(Long userId);

  /**
   * delete by roleId and userId.
   *
   * @param roleId roleId
   * @param userId userId
   * @return total data delete
   */
  @Transactional
  long deleteAllByRoleIdAndUserId(Long roleId, Long userId);

  /**
   * Delete All by roleId.
   *
   * @param roleId roleId
   * @return total data
   */
  @Transactional
  long deleteAllByRoleId(Long roleId);

  /**
   * delete all by list role id.
   *
   * @param roleIds roleIds
   * @return total data
   */
  @Transactional
  long deleteAllByRoleIdIn(List<Long> roleIds);

  /**
   * Delete all by userId.
   *
   * @param userId userId
   * @return total data
   */
  @Transactional
  long deleteAllByUserId(Long userId);

  /**
   * Delete all by list userId.
   *
   * @param userIds userIds
   * @return total data
   */
  @Transactional
  long deleteAllByUserIdIn(List<Long> userIds);
}
