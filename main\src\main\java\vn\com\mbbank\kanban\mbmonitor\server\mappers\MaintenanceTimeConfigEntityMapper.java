package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.MaintenanceTimeConfigRequest;

/**
 * Mapper logic MaintenanceConfigResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MaintenanceTimeConfigEntityMapper extends
    KanbanBaseMapper<MaintenanceTimeConfigEntity, MaintenanceTimeConfigRequest> {
  MaintenanceTimeConfigEntityMapper INSTANCE = Mappers.getMapper(MaintenanceTimeConfigEntityMapper.class);

  /**
   * map from AlertPriorityConfigEntity to AlertPriorityConfigResponse.
   *
   * @param entity  MaintenanceConfigEntity.
   * @param request MaintenanceConfigRequest.
   */
  void merge(@MappingTarget MaintenanceTimeConfigEntity entity, MaintenanceTimeConfigRequest request);
}
