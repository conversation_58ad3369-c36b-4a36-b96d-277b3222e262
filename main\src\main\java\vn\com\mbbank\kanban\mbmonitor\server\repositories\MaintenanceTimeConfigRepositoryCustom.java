package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.mbmonitor.common.enums.MaintenanceTimeConfigDependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.MaintenanceTimePaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.MaintenanceTimeConfigResponse;

/**
 * Repository table MaintenanceTimeConfig.
 */
public interface MaintenanceTimeConfigRepositoryCustom {

  /**
   * find all config by deleted and search.
   *
   * @param request param search
   * @return list of MaintenanceTimeConfigEntity
   */
  Page<MaintenanceTimeConfigResponse> findAllBySearch(MaintenanceTimePaginationRequest request);

  /**
   * get list name of maintenance time group config.
   *
   * @param dependencyId dependencyId
   * @param type         MaintenanceTimeConfigDependencyTypeEnum
   * @return list name of alert group config
   */
  List<String> findDependencyNameByDependencyId(String dependencyId,
                                                List<MaintenanceTimeConfigDependencyTypeEnum> type);
}
