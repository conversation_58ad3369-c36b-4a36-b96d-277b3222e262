package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.Map;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import vn.com.mbbank.kanban.core.utils.KanbanEntityUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.EmailTemplateEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.EmailTemplatePaginationModel;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.EmailTemplateRepositoryCustom;

/**
 * Implement EmailTemplateRepositoryCustom table EMAIL_TEMPLATE.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EmailTemplateRepositoryCustomImpl implements EmailTemplateRepositoryCustom {
  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public Page<EmailTemplateEntity> findAll(EmailTemplatePaginationModel request) {
    var query = new PrepareQuery("""
        SELECT emailTemplate.ID as id, emailTemplate.NAME as name, emailTemplate.SUBJECT as subject,
          emailTemplate.DESCRIPTION as description 
        FROM EMAIL_TEMPLATE emailTemplate
        LEFT JOIN EMAIL_TEMPLATE_RECEIVER emailTemplateReceiver
            ON emailTemplate.ID = emailTemplateReceiver.EMAIL_TEMPLATE_ID
        WHERE 1=1
        """).append(buildQuerySearchLike(request.getSearch()))
        .append(
            " GROUP BY emailTemplate.ID, emailTemplate.NAME, emailTemplate.SUBJECT, emailTemplate.DESCRIPTION, "
                + "emailTemplate.CREATED_DATE ");
    String sortColumn =
        KanbanEntityUtils.getColumnName(request.getSortBy(), EmailTemplateEntity.class);
    if (!StringUtils.isNullOrEmpty(sortColumn)) {
      query.append(" ORDER BY emailTemplate.").append(sortColumn);
      if (Objects.nonNull(request.getSortOrder())) {
        query.append(" ").append(request.getSortOrder().name()).append(", emailTemplate.ID");
      }
    }
    return sqlQueryUtil.queryModel()
        .queryPaging(query.getQuery(), query.getParams(), EmailTemplateEntity.class,
            PageRequest.of(request.getPage(), request.getSize()));
  }

  PrepareQuery buildQuerySearchLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    PrepareQuery prepareQuery = new PrepareQuery();
    prepareQuery.append(new PrepareQuery("AND LOWER(emailTemplate.NAME) LIKE :search "),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery("OR LOWER(emailTemplate.SUBJECT) LIKE :search "),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery("OR LOWER(emailTemplate.DESCRIPTION) LIKE :search "),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(emailTemplateReceiver.ADDRESS) LIKE :search ",
        Map.of("search", search.toLowerCase())), LikeMatcher.CONTAINING);
    return prepareQuery;
  }
}
