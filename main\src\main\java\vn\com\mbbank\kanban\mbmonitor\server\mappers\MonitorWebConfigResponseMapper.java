package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorWebConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.MonitorWebConfigResponse;

/**
 * Mapper logic MonitorWebConfigResponseMapper.
 *
 * <AUTHOR>
 * @created_date 30/05/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MonitorWebConfigResponseMapper
    extends KanbanBaseMapper<MonitorWebConfigResponse, MonitorWebConfigEntity> {
  MonitorWebConfigResponseMapper INSTANCE = Mappers.getMapper(MonitorWebConfigResponseMapper.class);
}