package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsGroupConfigEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/24/2025
 */
@Repository
public interface TeamsGroupConfigRepositoryCustom {
  /**
   * Find teams group by emails.
   *
   * @param  emails emails
   * @return TeamsGroupConfigEntity
   */
  TeamsGroupConfigEntity findGroupByEmails(List<String> emails);
}
