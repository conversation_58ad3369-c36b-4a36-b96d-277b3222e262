package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.MonitorWebConfigResponse;

/**
 * Custom Repo table MonitorWebConfig.
 */
public interface MonitorWebConfigRepositoryCustom {
  /**
   * find application name and service name by id.
   *
   * @param id The id of monitor web config.
   * @return The data of MonitorWebConfigResponse objects with id of MonitorWebConfig.
   */
  MonitorWebConfigResponse findAppNameAndServiceNameWithId(String id);
  
  /**
   * find all collect email config by paginationRequest.
   *
   * @param paginationRequest The PaginationRequestDTO.
   * @return The page data of MonitorWebConfigResponse objects with id of MonitorWebConfigResponse.
   */
  Page<MonitorWebConfigResponse> findAll(PaginationRequestDTO paginationRequest);
}
