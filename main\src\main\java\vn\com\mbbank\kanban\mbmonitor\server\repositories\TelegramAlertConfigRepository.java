package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TelegramAlertConfigEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/6/2025
 */
@Repository
public interface TelegramAlertConfigRepository
    extends JpaCommonRepository<TelegramAlertConfigEntity, String>,
    TelegramAlertConfigRepositoryCustom {
  /**
   * Delete telegram alert config by list application id.
   *
   * @param applicationIds applicationIds
   * @return total record delete
   */
  int deleteByApplicationIdIn(List<String> applicationIds);

  /**
   * Delete telegram alert config by list services id.
   *
   * @param serviceIds serviceIds
   * @return total record delete
   */
  int deleteByServiceIdIn(List<String> serviceIds);
}
