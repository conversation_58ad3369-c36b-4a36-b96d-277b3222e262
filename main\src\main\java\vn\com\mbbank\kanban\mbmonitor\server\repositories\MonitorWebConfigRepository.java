package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorWebConfigEntity;

/**
 * Repository table monitor web config.
 */
@Repository
public interface MonitorWebConfigRepository
    extends JpaCommonRepository<MonitorWebConfigEntity, String>, MonitorWebConfigRepositoryCustom {
  
  /**
   * find all monitor web configs by id of service.
   *
   * @param id The id of service.
   * @return The list of MonitorWebConfigEntity objects.
   */
  
  List<MonitorWebConfigEntity> findAllByServiceId(String id);
  
  /**
   * find all monitor web configs by id of application.
   *
   * @param id The id of application.
   * @return The list of MonitorWebConfigEntity objects.
   */
  
  List<MonitorWebConfigEntity> findAllByApplicationId(String id);
  
  /**
   * find all monitor web configs by id of v.
   *
   * @param id The id of alert priority config.
   * @return The list of MonitorWebConfigEntity objects.
   */
  
  List<MonitorWebConfigEntity> findAllByPriorityId(Long id);
  
  /**
   * exist of MonitorWebConfigEntity objects with the specified name.
   *
   * @param name The name to count by.
   * @return The true/false of MonitorWebConfigEntity objects with the specified name.
   */
  boolean existsByNameIgnoreCase(String name);
  
  /**
   * exist of MonitorWebConfigEntity objects with the specified name and excluding the specified id.
   *
   * @param name The name to count by.
   * @param id   The id to exclude.
   * @return The true/false of MonitorWebConfigEntity objects with the specified name and excluding id.
   */
  boolean existsByIdNotAndNameIgnoreCase(String id, String name);
  
}
