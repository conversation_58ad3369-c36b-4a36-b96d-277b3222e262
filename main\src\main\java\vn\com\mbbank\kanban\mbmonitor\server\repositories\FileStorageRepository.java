package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FileStorageEntity;

/**
 * Repository table FileStorageEntity.
 */
@Repository
public interface FileStorageRepository
    extends JpaCommonRepository<FileStorageEntity, Long> {
  /**
   * Deletes all records from the database that match the specified dependency name and dependency ID.
   *
   * @param dependencyName the name of the dependency to match records for deletion.
   * @param dependencyId   the unique identifier of the dependency to match records for deletion.
   */
  void deleteAllByDependencyNameAndDependencyId(String dependencyName, String dependencyId);

  /**
   * Find all records from the database that match the specified dependency name and dependency ID.
   *
   * @param dependencyName the name of the dependency to match records for deletion.
   * @param dependencyId   the unique identifier of the dependency to match records for deletion.
   * @return List of FileStorageEntity.
   */
  List<FileStorageEntity> findAllByDependencyNameAndDependencyId(String dependencyName, String dependencyId);

}
