package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskUserTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.TaskUserResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.TaskUserRepositoryCustom;

/**
 * TaskRepositoryCustomImpl.
 */
@RequiredArgsConstructor
public class TaskUserRepositoryCustomImpl implements TaskUserRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public List<TaskUserResponse> findAllByTaskIdAndType(Long taskId, TaskUserTypeEnum type) {
    var query = new PrepareQuery("""
        SELECT taskUser.ID,
               taskUser.TASK_ID,
               taskUser.USER_NAME,
               taskUser.TYPE,
               taskUser.CREATED_DATE,
               taskUser.CREATED_BY
        FROM TASK_USER taskUser
        WHERE 1 = 1
        """);
    if (Objects.nonNull(taskId)) {
      query.append(new PrepareQuery(" AND taskUser.TASK_ID = :taskId", "taskId", taskId));
    }
    if (Objects.nonNull(type)) {
      query.append(new PrepareQuery(" AND taskUser.TYPE = :type", "type", type.name()));
    }
    query.append(" ORDER BY taskUser.CREATED_DATE DESC ");
    return sqlQueryUtil.queryModel().queryForList(query.getQuery(), query.getParams(), TaskUserResponse.class);
  }
}