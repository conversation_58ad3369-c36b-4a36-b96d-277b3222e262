package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import java.util.List;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;

/**
 * Mapper logic AlertGroupConfigResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AlertGroupEntityMapper extends
    KanbanBaseMapper<AlertGroupEntity, AlertEntity> {

  AlertGroupEntityMapper INSTANCE = Mappers.getMapper(AlertGroupEntityMapper.class);

  /**
   * map from AlertPriorityConfigEntity to AlertPriorityConfigResponse.
   *
   * @param alert          alert entity.
   * @param serviceIds     list of serviceId
   * @param applicationIds list of applicationId
   * @return AlertGroupEntity.
   */
  default AlertGroupEntity map(AlertEntity alert, List<String> serviceIds,
                               List<String> applicationIds) {
    if (Objects.isNull(alert)) {
      return null;
    }
    var alertGroup = new AlertGroupEntity();
    alertGroup.setMatchValue(alert.getContent());
    alertGroup.setStatus(AlertGroupStatusEnum.NEW);
    alertGroup.setPrimaryAlertId(alert.getId());
    return alertGroup;
  }

  /**
   * map from AlertPriorityConfigEntity to AlertPriorityConfigResponse.
   *
   * @param alert              alert entity.
   * @param serviceIds         list of serviceId
   * @param applicationIds     list of applicationId
   * @param matchValue         matchValue
   * @param alertGroupConfigId alertGroupConfigId
   * @return AlertGroupEntity.
   */
  default AlertGroupEntity map(AlertEntity alert, List<String> serviceIds,
                               List<String> applicationIds,
                               String matchValue, Long alertGroupConfigId) {
    if (Objects.isNull(alert)) {
      return null;
    }
    var alertGroup = new AlertGroupEntity();
    alertGroup.setMatchValue(matchValue);
    alertGroup.setAlertGroupConfigId(alertGroupConfigId);
    alertGroup.setStatus(AlertGroupStatusEnum.NEW);
    alertGroup.setPrimaryAlertId(alert.getId());
    return alertGroup;
  }
}
