package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.TeamsConfigModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.TeamsConfigRequest;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/23/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TeamsConfigRequestToModelMapper extends
    KanbanBaseMapper<TeamsConfigRequest, TeamsConfigModel> {
  TeamsConfigRequestToModelMapper INSTANCE = Mappers.getMapper(TeamsConfigRequestToModelMapper.class);
}
