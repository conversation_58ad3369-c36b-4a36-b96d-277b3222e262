package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.TriggerExecutionResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.AutoTriggerActionConfigExecutionMapperRepositoryCustom;

/**
 * AutoTriggerActionConfigRepositoryCustomImpl.
 */
@RequiredArgsConstructor
public class AutoTriggerActionConfigExecutionMapperRepositoryCustomImpl
        implements AutoTriggerActionConfigExecutionMapperRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public List<TriggerExecutionResponse> findByAutoTriggerActionConfigIdIn(List<String> autoTriggerActionConfigIds) {
    if (CollectionUtils.isEmpty(autoTriggerActionConfigIds)) {
      return List.of();
    }

    var query = new PrepareQuery("""
        SELECT autoTriggerActionConfigExecutionMap.AUTO_TRIGGER_ACTION_CONFIG_ID AS autoTriggerActionConfigId,
               autoTriggerActionConfigExecutionMap.EXECUTION_ID AS executionId
        FROM AUTO_TRIGGER_ACTION_CONFIG_EXECUTION_MAPPER autoTriggerActionConfigExecutionMap
        WHERE autoTriggerActionConfigExecutionMap.AUTO_TRIGGER_ACTION_CONFIG_ID IN (:configIds)
        """, Map.of("configIds", autoTriggerActionConfigIds));

    return sqlQueryUtil.queryModel()
            .queryForList(query.getQuery(), query.getParams(), TriggerExecutionResponse.class);
  }
}
