package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.TeamsConfigRequest;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/23/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TeamsConfigRequestToEntityMapper extends
    KanbanBaseMapper<TeamsConfigRequest, TeamsConfigEntity> {
  TeamsConfigRequestToEntityMapper INSTANCE = Mappers.getMapper(TeamsConfigRequestToEntityMapper.class);
}