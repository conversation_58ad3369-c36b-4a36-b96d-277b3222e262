package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import java.util.Collections;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CollectEmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.condition.RuleGroupType;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.CollectEmailConfigRequest;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/30/2024
 */


@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring", imports = {
    RuleGroupType.class, Collections.class})
public interface CollectEmailConfigEntityMapper
    extends KanbanBaseMapper<CollectEmailConfigEntity, CollectEmailConfigRequest> {
  CollectEmailConfigEntityMapper INSTANCE = Mappers.getMapper(CollectEmailConfigEntityMapper.class);

  @Override
  @Mapping(target = "ruleGroup", expression = "java(resolveRuleGroup(source.getRuleGroup()))")
  CollectEmailConfigEntity map(CollectEmailConfigRequest source);

  @Override
  @Mapping(target = "ruleGroup", expression = "java(resolveRuleGroup(target.getRuleGroup()))")
  CollectEmailConfigRequest mapTo(CollectEmailConfigEntity target);

  @Override
  @Mapping(target = "ruleGroup", expression = "java(resolveRuleGroup(source.getRuleGroup()))")
  List<CollectEmailConfigEntity> map(List<CollectEmailConfigRequest> sources);

  @Override
  @Mapping(target = "ruleGroup", expression = "java(resolveRuleGroup(target.getRuleGroup()))")
  List<CollectEmailConfigRequest> mapTo(List<CollectEmailConfigEntity> targets);

  @Override
  default Page<CollectEmailConfigEntity> map(Page<CollectEmailConfigRequest> sourcePage) {
    return map(sourcePage);
  }

  @Override
  default Page<CollectEmailConfigRequest> mapTo(Page<CollectEmailConfigEntity> targetPage) {
    return mapTo(targetPage);
  }

  /**
   * custom mapper RuleGroupType.
   *
   * @param ruleGroup ruleGroup
   * @return ruleGroup
   */
  default RuleGroupType resolveRuleGroup(RuleGroupType ruleGroup) {
    return ruleGroup;
  }
}
