package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SysRoleWithPermissionsModel;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.SysRoleRepositoryCustom;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 9/23/2024
 */
@RequiredArgsConstructor
public class SysRoleRepositoryCustomImpl implements SysRoleRepositoryCustom {
  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public List<SysRoleWithPermissionsModel> findAllByUserIdIn(List<Long> userIds) {
    String query = """
        SELECT
            role.*, userRole.USER_ID
        FROM
            SYS_ROLE role
        JOIN SYS_USER_ROLE userRole ON
            role.ID = userRole.ROLE_ID
        WHERE
            userRole.USER_ID IN (:userIds)
            AND role.ACTIVE = '1'
        """;
    return sqlQueryUtil.queryModel()
        .queryForList(query, Map.of("userIds", userIds), SysRoleWithPermissionsModel.class);
  }


}
