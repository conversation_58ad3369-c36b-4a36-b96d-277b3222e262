package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ServiceResponse;

/**
 * Mapper logic ServiceResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ServiceResponseMapper extends KanbanBaseMapper<ServiceResponse, ServiceEntity> {
  ServiceResponseMapper INSTANCE = Mappers.getMapper(ServiceResponseMapper.class);
}
