package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.enums.ModifyAlertConfigDependencyTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.ModifyAlertConfigRepositoryCustom;

/**
 * ModifyAlertConfigRepositoryCustomImpl.
 */
@RequiredArgsConstructor
public class ModifyAlertConfigRepositoryCustomImpl implements ModifyAlertConfigRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public List<String> findDependencyNameByDependencyId(String dependencyId,
                                                       List<ModifyAlertConfigDependencyTypeEnum> type) {
    var query = new PrepareQuery("""
        SELECT modifyAlertConfig.name FROM MODIFY_ALERT_CONFIG modifyAlertConfig
        LEFT JOIN MODIFY_ALERT_CONFIG_DEPENDENCY modifyAlertConfigDependency
          ON modifyAlertConfig.ID = modifyAlertConfigDependency.MODIFY_ALERT_CONFIG_ID
        WHERE 1=1
        """);
    if (StringUtils.isNotBlank(dependencyId)) {
      query.append(" AND modifyAlertConfigDependency.DEPENDENCY_ID = :dependencyId", "dependencyId",
          dependencyId);
    }
    if (Objects.nonNull(type)) {
      query.append(" AND modifyAlertConfigDependency.TYPE IN (:type)", "type",
          type.stream().map(Enum::name).toList());
    }
    return sqlQueryUtil.queryModel()
        .queryForList(query.getQuery(), query.getParams(), String.class);
  }

}
