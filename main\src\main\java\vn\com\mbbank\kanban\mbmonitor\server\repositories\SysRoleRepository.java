package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysRoleEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 9/23/2024
 */
@Repository
public interface SysRoleRepository
    extends JpaCommonRepository<SysRoleEntity, Long>, SysRoleRepositoryCustom {
  /**
   * Check exist roleName.
   *
   * @param name name
   * @param id   id
   * @return true/false
   */
  boolean existsByNameAndIdNot(String name, Long id);

  /**
   * Check exist roleName.
   *
   * @param name name
   * @return true/false
   */
  boolean existsByName(String name);

  /**
   * set active role.
   *
   * @param id     id
   * @param active active
   * @return total row
   */
  @Transactional
  @Modifying
  @Query(value = "UPDATE SYS_ROLE SET ACTIVE = :active WHERE id = :id", nativeQuery = true)
  int setActiveById(Long id, boolean active);
}
