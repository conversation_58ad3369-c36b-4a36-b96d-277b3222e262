package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.BaseSoftRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TaskEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskTypeEnum;

/**
 * Repository table  Task.
 */
@Repository
public interface TaskRepository
    extends BaseSoftRepository<TaskEntity, Long>, TaskRepositoryCustom {

  /**
   * find all task by id, type and status.
   *
   * @param ids     list of taskId
   * @param type    type of task
   * @param statues list of status
   * @return list of task
   */
  List<TaskEntity> findAllByIdInAndTypeAndStatusIn(List<Long> ids, TaskTypeEnum type, List<TaskStatusEnum> statues);

  /**
   * find all task type and status.
   *
   * @param type    type of task
   * @param statues list of status
   * @param deleted deleted status
   * @return list of task
   */
  List<TaskEntity> findAllByTypeAndStatusAndDeleted(TaskTypeEnum type, TaskStatusEnum statues, Boolean deleted);
}