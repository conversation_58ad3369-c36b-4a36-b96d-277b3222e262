package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertPriorityConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertPriorityConfigRequest;

/**
 * Mapper logic AlertPriorityConfigEntity.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AlertPriorityConfigEntityMapper
    extends KanbanBaseMapper<AlertPriorityConfigEntity, AlertPriorityConfigRequest> {

  AlertPriorityConfigEntityMapper INSTANCE =
      Mappers.getMapper(AlertPriorityConfigEntityMapper.class);

  /**
   * Merge AlertPriorityConfigRequest to AlertPriorityConfigEntity.
   *
   * @param entity  a AlertPriorityConfigEntity
   * @param request a AlertPriorityConfigRequest
   */
  void merge(@MappingTarget AlertPriorityConfigEntity entity, AlertPriorityConfigRequest request);

}
