package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TaskEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.TaskRequest;

/**
 * Information of Author.
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TaskEntityMapper extends KanbanBaseMapper<TaskEntity, TaskRequest> {
  TaskEntityMapper INSTANCE = Mappers.getMapper(TaskEntityMapper.class);

  /**
   * merge task request to task entity.
   *
   * @param entity  taskEntity.
   * @param request taskRequest.
   */
  void merge(@MappingTarget TaskEntity entity, TaskRequest request);
}