package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysUserRefreshTokenEntity;

/**
 * Repository table SysUserRefreshTokenRepository.
 */
@Repository
public interface SysUserRefreshTokenRepository
        extends JpaCommonRepository<SysUserRefreshTokenEntity, String> {
  /**
   * find SysUserRefreshTokenEntity by refreshToken.
   *
   * @param refreshToken to get SysUserRefreshTokenEntity
   * @return SysUserRefreshTokenEntity
   */
  Optional<SysUserRefreshTokenEntity> findByRefreshToken(String refreshToken);

  /**
   * find list SysUserRefreshTokenEntity by username.
   *
   * @param userName to find list
   * @return SysUserRefreshTokenEntity
   */
  List<SysUserRefreshTokenEntity> findAllByUserName(String userName);

  /**
   * delete expired token.
   *
   * @param ids to delete
   * @return void
   */
  long deleteByIdIn(List<String> ids);

  /**
   * delete by refresh token.
   *
   * @param refreshToken for delete
   * @return number of records
   */
  @Modifying
  @Transactional
  @Query(value = "DELETE FROM SYS_USER_REFRESH_TOKEN WHERE REFRESH_TOKEN = :refreshToken", nativeQuery = true)
  int deleteByRefreshToken(String refreshToken);
}


