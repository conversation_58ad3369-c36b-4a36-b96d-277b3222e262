package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigConditionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CustomObjectEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupConfigTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.AlertGroupConfigRequest;

/**
 * Mapper logic AlertGroupConfigResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AlertGroupConfigConditionEntityMapper extends
    KanbanBaseMapper<AlertGroupConfigConditionEntity, AlertGroupConfigRequest> {
  AlertGroupConfigConditionEntityMapper INSTANCE =
      Mappers.getMapper(AlertGroupConfigConditionEntityMapper.class);

  /**
   * map from AlertPriorityConfigEntity to AlertPriorityConfigResponse.
   *
   * @param request          AlertGroupConfigRequest.
   * @param alertGroupConfig alertGroupConfig.
   * @param customObjects    custom objecst
   * @return AlertPriorityConfigResponse
   */
  default List<AlertGroupConfigConditionEntity> map(
      AlertGroupConfigRequest request, AlertGroupConfigEntity alertGroupConfig,
      List<CustomObjectEntity> customObjects) {
    if (Objects.isNull(request) || Objects.isNull(request.getType())) {
      return new ArrayList<>();
    }
    if (AlertGroupConfigTypeEnum.SAME_OBJECT_VALUE.equals(request.getType())) {
      if (CollectionUtils.isEmpty(customObjects)) {
        return new ArrayList<>();
      }
      return customObjects.stream().map(customObject -> {
        var alertGroupConfigCondition = new AlertGroupConfigConditionEntity();
        alertGroupConfigCondition.setCustomObjectId(customObject.getId());
        alertGroupConfigCondition.setAlertGroupConfigId(alertGroupConfig.getId());
        return alertGroupConfigCondition;
      }).toList();
    }
    if (AlertGroupConfigTypeEnum.MULTIPLE_CONDITION.equals(request.getType())) {
      if (CollectionUtils.isEmpty(request.getRuleGroups())) {
        return new ArrayList<>();
      }
      return request.getRuleGroups().stream().map(ruleGroup -> {
        var alertGroupConfigCondition = new AlertGroupConfigConditionEntity();
        alertGroupConfigCondition.setRuleGroup(ruleGroup);
        alertGroupConfigCondition.setAlertGroupConfigId(alertGroupConfig.getId());
        return alertGroupConfigCondition;
      }).toList();
    }
    return new ArrayList<>();
  }
}
