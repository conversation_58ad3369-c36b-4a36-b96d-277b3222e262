package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.AlertEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.AlertResponse;

/**
 * Mapper logic AlertResponse.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AlertResponseMapper extends KanbanBaseMapper<AlertResponse, AlertEntity> {
  AlertResponseMapper INSTANCE = Mappers.getMapper(AlertResponseMapper.class);
}
