package vn.com.mbbank.kanban.mbmonitor.server.repositories;


import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEventTargetEntity;

/**
 * Repository for NotificationEventTargetEntity.
 */
@Repository
public interface NotificationEventTargetRepository
    extends JpaCommonRepository<NotificationEventTargetEntity, String> {

  /**
   * find all by notificationEventId.
   *
   * @param id notificationEventId
   * @return list of NotificationEventTargetEntity
   */
  List<NotificationEventTargetEntity> findAllByNotificationEventId(String id);

  /**
   * find all by list notificationEventId.
   *
   * @param ids list notificationEventId
   * @return list of NotificationEventTargetEntity
   */
  List<NotificationEventTargetEntity> findAllByNotificationEventIdIn(List<String> ids);

  /**
   * delete all by notificationEventId.
   *
   * @param id notificationEventId
   */
  void deleteAllByNotificationEventId(String id);
}