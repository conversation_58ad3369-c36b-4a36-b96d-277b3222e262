package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.Map;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEntityUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.CollectEmailConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.CollectEmailConfigModel;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.CollectEmailConfigRepositoryCustom;

/**
 * Implement CollectEmailConfigRepositoryCustom table CollectEmailConfig.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CollectEmailConfigRepositoryImpl implements CollectEmailConfigRepositoryCustom {
  SqlQueryUtil sqlQueryUtil;


  @Override
  public CollectEmailConfigModel findCollectEmailConfigById(Long id) {
    var query = new PrepareQuery(
        """
            SELECT collectEmailConfig.ID,
                   collectEmailConfig.NAME,
                   collectEmailConfig.DESCRIPTION,
                   collectEmailConfig.RULE_GROUP AS ruleGroupColumn,
                   collectEmailConfig.CONTENT,
                   collectEmailConfig.CONTENT_TYPE,
                   collectEmailConfig.TYPE,
                   collectEmailConfig.ABSENCE_INTERVAL,
                   collectEmailConfig.ALERT_REPEAT_INTERVAL,
                   collectEmailConfig.RECIPIENT,
                   collectEmailConfig.ACTIVE,
                   collectEmailConfig.CONTENT_VALUE,
                   collectEmailConfig.PRIORITY_CONFIG_ID AS priorityConfigId,
                   emailConfig.INTERVAL_TIME AS emailConfigIntervalTime,
                   collectEmailConfig.EMAIL_CONFIG_ID,
                   emailConfig.EMAIL AS emailConfigEmail,
                   collectEmailConfig.APPLICATION_ID,
                   collectEmailConfig.SERVICE_ID,
                   application.NAME AS applicationName,
                   service.NAME AS serviceName
                    FROM COLLECT_EMAIL_CONFIG collectEmailConfig
                     JOIN EMAIL_CONFIG emailConfig ON emailConfig.ID= collectEmailConfig.EMAIL_CONFIG_ID
                     JOIN APPLICATION application ON application.ID= collectEmailConfig.APPLICATION_ID
                     JOIN SERVICE service ON service.ID= collectEmailConfig.SERVICE_ID
            """
    ).append("WHERE collectEmailConfig.ID= :id", "id", id);
    return sqlQueryUtil.queryModel()
        .queryForObject(query.getQuery(), query.getParams(), CollectEmailConfigModel.class);
  }

  @Override
  public Page<CollectEmailConfigModel> findAll(PaginationRequestDTO paginationRequest) {
    var query = new PrepareQuery("""
        SELECT collectEmailConfig.ID AS id, collectEmailConfig.NAME AS name, emailConfig.EMAIL AS emailConfigEmail,
        collectEmailConfig.DESCRIPTION as description, collectEmailConfig.ACTIVE as active,
        collectEmailConfig.TYPE as type, collectEmailConfig.RULE_GROUP AS ruleGroupColumn,
        emailConfig.INTERVAL_TIME as emailConfigIntervalTime
        FROM COLLECT_EMAIL_CONFIG collectEmailConfig
        JOIN EMAIL_CONFIG emailConfig ON collectEmailConfig.EMAIL_CONFIG_ID=emailConfig.ID
        WHERE 1=1
        """
    ).append(buildQuerySearchLike(paginationRequest.getSearch()));

    String sortColumn = KanbanEntityUtils.getColumnName(paginationRequest.getSortBy(),
        CollectEmailConfigEntity.class);
    if (!KanbanCommonUtil.isEmpty(sortColumn)) {
      query.append(" ORDER BY collectEmailConfig.").append(sortColumn);
    } else if ("emailConfigIntervalTime".equals(paginationRequest.getSortBy())) {
      query.append(" ORDER BY emailConfig.INTERVAL_TIME");
    } else if ("emailConfigEmail".equals(paginationRequest.getSortBy())) {
      query.append(" ORDER BY emailConfig.EMAIL");
    }
    if (paginationRequest.getSortOrder() != null) {
      query.append(" ").append(paginationRequest.getSortOrder().name());
    }
    Pageable pageable = PageRequest.of(paginationRequest.getPage(),
        paginationRequest.getSize());
    return sqlQueryUtil.queryModel()
        .queryPaging(query.getQueryBuilder().toString(), query.getParams(),
            CollectEmailConfigModel.class, pageable);

  }

  PrepareQuery buildQuerySearchLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    PrepareQuery prepareQuery = new PrepareQuery();
    prepareQuery.append(new PrepareQuery("AND LOWER(collectEmailConfig.NAME) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(collectEmailConfig.DESCRIPTION) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(emailConfig.INTERVAL_TIME) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(collectEmailConfig.TYPE) LIKE :typeSearch",
        Map.of("typeSearch", search.replace(" ", "_").toLowerCase())), LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(emailConfig.EMAIL) LIKE :search",
        Map.of("search", search.toLowerCase())), LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(collectEmailConfig.RULE_GROUP) LIKE :search",
        Map.of("search", search.toLowerCase())), LikeMatcher.CONTAINING);
    return prepareQuery;
  }

}
