package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TaskEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.TaskCursor;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.TaskSearchParamRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.TaskSearchRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.CursorPageResponse;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.TaskResponse;

/**
 * TaskRepositoryCustom.
 */
public interface TaskRepositoryCustom {

  /**
   * find all task with paging.
   *
   * @param searchRequest searchRequest
   * @param requestParam  cursorRequest
   * @return page of task
   */
  CursorPageResponse<TaskResponse, TaskCursor> findAllWithPaging(TaskSearchRequest searchRequest,
                                                                 TaskSearchParamRequest requestParam);

  /**
   * find all task.
   *
   * @param searchRequest searchRequest
   * @return list of task
   */
  List<TaskEntity> findAll(TaskSearchRequest searchRequest);

  /**
   * find all task by parentTaskId and type.
   *
   * @param parentTaskId parentTaskId
   * @param type         type of task
   * @return list of task
   */
  List<TaskResponse> findAllByParentTaskIdAndTypeAndStatus(Long parentTaskId, TaskTypeEnum type);
}