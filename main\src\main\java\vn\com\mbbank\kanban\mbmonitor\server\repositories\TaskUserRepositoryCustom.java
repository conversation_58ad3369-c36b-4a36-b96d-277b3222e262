package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TaskUserTypeEnum;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.TaskUserResponse;

/**
 * TaskUserRepositoryCustom.
 */
public interface TaskUserRepositoryCustom {

  /**
   * find all taskUser by taskId, type.
   *
   * @param taskId taskId
   * @param type   type of task
   * @return list of taskUser
   */
  List<TaskUserResponse> findAllByTaskIdAndType(Long taskId, TaskUserTypeEnum type);
}