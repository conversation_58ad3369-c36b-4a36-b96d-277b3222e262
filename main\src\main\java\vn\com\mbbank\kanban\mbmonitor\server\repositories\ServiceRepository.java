package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.BaseSoftRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ServiceEntity;

/**
 * Repository table  Service.
 */
@Repository
public interface ServiceRepository
    extends BaseSoftRepository<ServiceEntity, String>, ServiceRepositoryCustom {
  /**
   * Find first by name ignore case.
   *
   * @param serviceName serviceName
   * @return ServiceEntity
   */
  Optional<ServiceEntity> findFirstByNameIgnoreCase(String serviceName);

  /**
   * Counts the number of ServiceEntity objects with the specified name.
   *
   * @param name The name to count by.
   * @return The number of ServiceEntity objects with the specified name.
   */
  long countByNameIgnoreCaseAndDeletedIsFalse(String name);

  /**
   * Counts the number of ServiceEntity objects with the specified name and excluding the specified id.
   *
   * @param name The name to count by.
   * @param id   The id to exclude.
   * @return The number of ServiceEntity objects with the specified name and excluding the specified id.
   */
  long countByIdNotAndNameIgnoreCaseAndDeletedIsFalse(String id, String name);

  /**
   * find all service by ids.
   *
   * @param ids list ids.
   * @return List of ServiceEntity.
   */
  List<ServiceEntity> findAllByIdIn(List<String> ids);

  /**
   * find all service by ids and deleted is false.
   *
   * @param ids list ids.
   * @return List of ServiceEntity.
   */
  List<ServiceEntity> findAllByIdInAndDeletedIsFalse(List<String> ids);

  /**
   * Get next sequence id of SERVICE_SEQ.
   *
   * @return value sequence
   */
  @Query(value = "SELECT SERVICE_SEQ.nextval FROM dual", nativeQuery = true)
  long getNextSequenceValue();

  /**
   * Find all by id and deleted status.
   *
   * @param ids     list ids.
   * @param deleted deleted status
   * @return list of service
   */
  List<ServiceEntity> findAllByIdInAndDeleted(List<String> ids, boolean deleted);
  
  /**
   * Find the name of service by id.
   *
   * @param id      id.
   * @return name
   */
  @Query(value = "SELECT service.NAME FROM SERVICE service WHERE service.ID = :id", nativeQuery = true)
  String findNameById(String id);
  
  
}
