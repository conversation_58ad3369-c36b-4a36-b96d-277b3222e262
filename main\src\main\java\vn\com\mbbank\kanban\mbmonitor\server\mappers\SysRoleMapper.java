package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.SysRoleWithPermissionsModel;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysRoleEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 9/24/2024
 */
@Mapper(componentModel = "spring")
public interface SysRoleMapper
    extends KanbanBaseMapper<SysRoleWithPermissionsModel, SysRoleEntity> {
  SysRoleMapper INSTANCE = Mappers.getMapper(SysRoleMapper.class);
}
