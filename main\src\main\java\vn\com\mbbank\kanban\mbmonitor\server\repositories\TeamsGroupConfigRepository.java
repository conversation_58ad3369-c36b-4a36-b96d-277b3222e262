package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.Optional;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsGroupConfigEntity;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/24/2025
 */
@Repository
public interface TeamsGroupConfigRepository
    extends JpaCommonRepository<TeamsGroupConfigEntity, String>, TeamsGroupConfigRepositoryCustom {

  /**
   * get teams group chat by group name.
   *
   * @param groupName groupName
   * @return TeamsGroupConfigEntity
   */
  Optional<TeamsGroupConfigEntity> findFirstByTeamsGroupNameIgnoreCase(String groupName);


}
