package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.Map;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.KanbanEntityUtils;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.core.utils.StringUtils;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MonitorWebConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.utils.LikeMatcher;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.MonitorWebConfigResponse;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.MonitorWebConfigRepositoryCustom;

/**
 * Implement MonitorWebConfigRepositoryCustom table MonitorWebConfig.
 */
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class MonitorWebConfigRepositoryCustomImpl implements MonitorWebConfigRepositoryCustom {
  SqlQueryUtil sqlQueryUtil;
  
  @Override
  public MonitorWebConfigResponse findAppNameAndServiceNameWithId(String id) {
    var query = new PrepareQuery(
          """
            SELECT
              monitorWebConfig.ID AS id,
              service.NAME AS serviceName,
              application.NAME AS applicationName
            FROM MONITOR_WEB_CONFIG monitorWebConfig
            JOIN SERVICE service ON service.ID = monitorWebConfig.SERVICE_ID
            JOIN APPLICATION application ON application.ID = monitorWebConfig.APPLICATION_ID
          """
    ).append("WHERE monitorWebConfig.ID= :id", "id", id);
    return sqlQueryUtil.queryModel()
      .queryForObject(query.getQuery(), query.getParams(), MonitorWebConfigResponse.class);
  }
  
  @Override
  public Page<MonitorWebConfigResponse> findAll(PaginationRequestDTO paginationRequest) {
    var query = new PrepareQuery(
        """
          SELECT
            monitorWebConfig.ID AS id,
            monitorWebConfig.NAME AS name,
            monitorWebConfig.DESCRIPTION AS description,
            monitorWebConfig.WEB_URL AS webUrl,
            monitorWebConfig.MONITOR_TYPE AS monitorType,
            monitorWebConfig.TIME_OUT AS timeout,
            monitorWebConfig.BROWSER  AS browser,
            monitorWebConfig.ACTIVE  AS active
            FROM MONITOR_WEB_CONFIG monitorWebConfig
            WHERE 1=1
        """
    ).append(buildQuerySearchLike(paginationRequest.getSearch()));
    
    String sortColumn = KanbanEntityUtils.getColumnName(paginationRequest.getSortBy(),
        MonitorWebConfigEntity.class);
    if (!KanbanCommonUtil.isEmpty(sortColumn)) {
      query.append(" ORDER BY monitorWebConfig.").append(sortColumn);
    }
    if (paginationRequest.getSortOrder() != null) {
      query.append(" ").append(paginationRequest.getSortOrder().name());
    }
    Pageable pageable = PageRequest.of(paginationRequest.getPage(),
        paginationRequest.getSize());
    return sqlQueryUtil.queryModel()
      .queryPaging(query.getQueryBuilder().toString(), query.getParams(),
        MonitorWebConfigResponse.class, pageable);
    
  }
  
  PrepareQuery buildQuerySearchLike(String search) {
    if (StringUtils.isBlank(search)) {
      return null;
    }
    PrepareQuery prepareQuery = new PrepareQuery();
    prepareQuery.append(new PrepareQuery("AND (LOWER(monitorWebConfig.NAME) LIKE :search"), LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(monitorWebConfig.DESCRIPTION) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(monitorWebConfig.WEB_URL) LIKE :search"), LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(monitorWebConfig.MONITOR_TYPE) LIKE :search"),
        LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(monitorWebConfig.TIME_OUT) LIKE :search"), LikeMatcher.CONTAINING);
    prepareQuery.append(new PrepareQuery(" OR LOWER(monitorWebConfig.BROWSER) LIKE :search",
        Map.of("search", search.toLowerCase())), LikeMatcher.CONTAINING);
    prepareQuery.append(" )");
    return prepareQuery;
  }
}
