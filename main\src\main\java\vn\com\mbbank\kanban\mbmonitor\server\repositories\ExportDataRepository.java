package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ExportDataEntity;

/**
 * Repository table  ExportDataEntity.
 */
@Repository
public interface ExportDataRepository extends JpaCommonRepository<ExportDataEntity, String> {
  /**
   * Updates the status for the export data entity with the given ID.
   *
   * @param id     the export data entity ID to update
   * @param status the new status as a {@code String}
   */
  @Modifying
  @Transactional
  @Query(value = "UPDATE EXPORT_DATA exportData SET exportData.STATUS = :status "
      + "WHERE exportData.ID = :id", nativeQuery = true)
  void updateStatusById(@Param("id") String id,
                        @Param("status") String status);
}
