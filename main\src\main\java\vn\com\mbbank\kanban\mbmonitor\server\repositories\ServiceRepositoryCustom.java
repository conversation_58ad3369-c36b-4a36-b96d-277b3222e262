package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import vn.com.mbbank.kanban.mbmonitor.common.enums.AlertGroupStatusEnum;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.ServiceWithPriorityModel;

/**
 * Custom Repo table Service.
 */
public interface ServiceRepositoryCustom {
  /**
   * Find service by alert status.
   *
   * @param alertGroupStatus alertGroupStatus
   * @return list service
   */
  List<ServiceWithPriorityModel> findServiceWithPriorityByAlertGroupStatus(
      AlertGroupStatusEnum alertGroupStatus);

}
