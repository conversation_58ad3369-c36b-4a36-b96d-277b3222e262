package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.Optional;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.TeamsConfigTypeEnum;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/16/2025
 */
public interface TeamsConfigRepository extends JpaCommonRepository<TeamsConfigEntity, String> {
  /**
   * find config teams by type.
   *
   * @param configType configType
   * @return TeamsConfigEntity
   */
  Optional<TeamsConfigEntity> findFirstByType(TeamsConfigTypeEnum configType);

}
