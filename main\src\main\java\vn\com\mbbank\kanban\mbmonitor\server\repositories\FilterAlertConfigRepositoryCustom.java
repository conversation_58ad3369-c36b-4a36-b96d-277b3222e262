package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.FilterAlertPaginationRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.FilterAlertConfigResponse;

/**
 * Repository table FilterAlertConfig.
 */
public interface FilterAlertConfigRepositoryCustom {

  /**
   * find all config by deleted and search.
   *
   * @param request param search
   * @return list of FilterAlertConfigResponse
   */
  Page<FilterAlertConfigResponse> findAllBySearch(FilterAlertPaginationRequest request);

}
