package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.requests.AlertRequestRequest;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.request.DatabaseThresholdConfigRequest;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 06/11/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AlertRequestRequestToDatabaseThresholdConfigMapper extends
    KanbanBaseMapper<DatabaseThresholdConfigRequest, AlertRequestRequest> {
  AlertRequestRequestToDatabaseThresholdConfigMapper INSTANCE =
      Mappers.getMapper(AlertRequestRequestToDatabaseThresholdConfigMapper.class);
}