package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import lombok.RequiredArgsConstructor;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.DatabaseCollectModel;
import vn.com.mbbank.kanban.mbmonitor.common.utils.PrepareQuery;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.DatabaseCollectRepositoryCustom;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 11/18/2024
 */
@RequiredArgsConstructor
public class DatabaseCollectRepositoryCustomImpl implements DatabaseCollectRepositoryCustom {
  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public DatabaseCollectModel findByIdWithNameDetails(Long id) {
    var query = new PrepareQuery("""
               SELECT
                  databaseCollect.*,
                  service.NAME AS serviceName,
                  application.NAME AS applicationName
              FROM
                  DATABASE_COLLECT databaseCollect
              LEFT JOIN SERVICE service ON
                  databaseCollect.SERVICE_ID = service.ID
              LEFT JOIN APPLICATION application ON
                  databaseCollect.APPLICATION_ID = application.ID
              WHERE databaseCollect.ID = :id
        """, "id", id);
    return sqlQueryUtil.queryModel()
        .queryForObject(query.getQuery(), query.getParams(), DatabaseCollectModel.class);
  }
}
