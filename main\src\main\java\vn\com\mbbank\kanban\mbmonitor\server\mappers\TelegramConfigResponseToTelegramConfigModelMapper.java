package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.model.TelegramConfigModel;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.TelegramConfigResponse;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/25/2025
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface TelegramConfigResponseToTelegramConfigModelMapper extends
    KanbanBaseMapper<TelegramConfigResponse, TelegramConfigModel> {
  TelegramConfigResponseToTelegramConfigModelMapper INSTANCE =
      Mappers.getMapper(TelegramConfigResponseToTelegramConfigModelMapper.class);
}
