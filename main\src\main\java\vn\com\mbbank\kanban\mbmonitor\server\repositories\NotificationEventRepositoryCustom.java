package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.mbmonitor.common.entities.NotificationEventEntity;

/**
 * Custom repository for NotificationEventEntity.
 */
public interface NotificationEventRepositoryCustom {

  /**
   * Find all notification events with pagination.
   *
   * @param paginationRequest Pagination request
   * @return Page of notification events
   */
  Page<NotificationEventEntity> findAll(PaginationRequestDTO paginationRequest);
}