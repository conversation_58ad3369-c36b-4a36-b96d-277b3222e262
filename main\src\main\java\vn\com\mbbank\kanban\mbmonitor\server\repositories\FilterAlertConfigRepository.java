package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.FilterAlertConfigEntity;

/**
 * Repository interface for managing filter alert configuration data in the system.
 */
@Repository
public interface FilterAlertConfigRepository
    extends JpaCommonRepository<FilterAlertConfigEntity, Long>, FilterAlertConfigRepositoryCustom {
  /**
   * check filter alert config existed by id, name and deleted status.
   *
   * @param id   id of config
   * @param name name of config
   * @return existed or not
   */
  boolean existsByIdNotAndNameIgnoreCase(Long id, String name);

  /**
   * check filter alert config existed by name.
   *
   * @param name name of config
   * @return existed or not
   */
  boolean existsByNameIgnoreCase(String name);


  /**
   * find all filter alert config by active.
   *
   * @param active active status
   * @return next position.
   */
  List<FilterAlertConfigEntity> findAllByActive(Boolean active);


}
