package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.Collection;
import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.VariableEntity;

/**
 * Repository table Variable.
 */
@Repository
public interface VariableRepository
    extends JpaCommonRepository<VariableEntity, String>, VariableRepositoryCustom {
  
  /**
   * check variable existed by id, name.
   *
   * @param name variables  name
   * @param id variables  id
   * @return existed or not
   */
  boolean existsByIdNotAndNameIgnoreCase(String id, String name);
  
  /**
   * check variable existed in name.
   *
   * @param names variables  name
   * @return existed or not
   */
  boolean existsByNameInIgnoreCase(Collection<String> names);

  /**
   * check variable  existed by name.
   *
   * @param name variable  name
   * @return existed or not
   */
  boolean existsByNameIgnoreCase(String name);

  /**
   * find variable by names.
   *
   * @param names variable variable name
   * @return list of variable
   */
  List<VariableEntity> findAllByNameIn(List<String> names);
  
  /**
   * find variable by executionId.
   *
   * @param executionId executionId
   * @return list of variable
   */
  List<VariableEntity> findAllByExecutionId(String executionId);
  
  /**
   * Delete all variable by executionId.
   *
   * @param executionId executionId
   */
  void deleteAllByExecutionId(String executionId);
  
  /**
   * check variable existed by execution.
   *
   * @param executionId variable executionId
   * @return existed or not
   */
  boolean existsByExecutionId(String executionId);
  
}
