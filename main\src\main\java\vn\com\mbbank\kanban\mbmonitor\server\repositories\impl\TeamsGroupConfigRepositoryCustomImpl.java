package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.entities.TeamsGroupConfigEntity;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.TeamsGroupConfigRepositoryCustom;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 4/24/2025
 */
@RequiredArgsConstructor
public class TeamsGroupConfigRepositoryCustomImpl implements TeamsGroupConfigRepositoryCustom {
  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public TeamsGroupConfigEntity findGroupByEmails(List<String> emails) {
    String query = """
        SELECT teamsGroupConfig.*
         FROM TEAMS_GROUP_CONFIG teamsGroupConfig
         JOIN (
             SELECT teamsUserConfig.TEAMS_GROUP_ID
             FROM TEAMS_USER_CONFIG teamsUserConfig
             WHERE teamsUserConfig.EMAIL IN (:emails)
             GROUP BY teamsUserConfig.TEAMS_GROUP_ID
             HAVING COUNT(*) = :totalEmail
                AND COUNT(*) = (
                    SELECT COUNT(*)
                    FROM TEAMS_USER_CONFIG
                    WHERE TEAMS_GROUP_ID = teamsUserConfig.TEAMS_GROUP_ID
                )
         ) matched_groups
         ON teamsGroupConfig.ID = matched_groups.TEAMS_GROUP_ID
         WHERE teamsGroupConfig.TEAMS_CONFIG_ID IS NOT NULL
         ORDER BY teamsGroupConfig."POSITION" ASC
         FETCH FIRST 1 ROW ONLY
        
        """;
    Map<String, Object> map = new HashMap<>();
    map.put("emails", emails);
    map.put("totalEmail", emails.size());
    return sqlQueryUtil.queryModel().queryForObject(query, map, TeamsGroupConfigEntity.class);
  }
}
