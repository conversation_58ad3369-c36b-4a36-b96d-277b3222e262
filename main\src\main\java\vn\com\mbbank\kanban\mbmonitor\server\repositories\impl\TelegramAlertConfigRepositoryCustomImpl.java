package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.core.utils.KanbanCommonUtil;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.models.telegram.TelegramAlertConfigModel;
import vn.com.mbbank.kanban.mbmonitor.common.utils.SqlUtils;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.TelegramAlertConfigRepositoryCustom;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 2/7/2025
 */
@Service
@RequiredArgsConstructor
public class TelegramAlertConfigRepositoryCustomImpl implements
    TelegramAlertConfigRepositoryCustom {

  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public Page<TelegramAlertConfigModel> findServicesWithPage(PaginationRequestDTO page) {
    String query = """
        SELECT
            service.ID as serviceId,
            service.NAME as serviceName,
            config.TELEGRAM_CONFIG_ID as telegramConfigId,
            'SERVICE' as type,
            config.GROUP_CHAT_ID as groupChatId,
            COALESCE(config.ACTIVE, 1) as active
        FROM
            SERVICE service
        LEFT JOIN TELEGRAM_ALERT_CONFIG config ON
            SERVICE.ID = config.SERVICE_ID
        WHERE
            SERVICE.DELETED = 0
        """;
    StringBuilder queryBuilder = new StringBuilder(query);
    Map<String, String> mapColumns =
        Map.of("serviceName", "service.Name", "name", "service.Name", "groupChatId",
            "config.GROUP_CHAT_ID", "createdDate",
            "config.CREATED_DATE");
    var customSearch = !KanbanCommonUtil.isEmpty(page.getSearch())
        ?
        """
            EXISTS (SELECT 1 FROM APPLICATION app 
            WHERE
            app.SERVICE_ID = service.ID AND app.DELETED = 0 AND LOWER(app.Name) like :search ESCAPE '\\')
        """ :
        "";
    queryBuilder.append(SqlUtils.buildPageQuery(page, mapColumns, customSearch));
    Map<String, Object> mapParam = new HashMap<>();
    mapParam.put("search", SqlUtils.getSearchEscape(page.getSearch().toLowerCase()));
    return sqlQueryUtil.queryModel()
        .queryPaging(queryBuilder.toString(), mapParam, TelegramAlertConfigModel.class,
            PageRequest.of(page.getPage(), page.getSize()));
  }

  @Override
  public Page<TelegramAlertConfigModel> findApplicationByServiceIdWithPage(String serviceId,
                                                                           PaginationRequestDTO page) {
    String query = """
        SELECT
            config.*,
            'APPLICATION' as type,
            app.ID AS applicationId,
            app.NAME AS applicationName
            
        FROM
            APPLICATION app
        LEFT JOIN TELEGRAM_ALERT_CONFIG config ON
            app.ID = config.APPLICATION_ID 
        WHERE
            app.DELETED = 0
            AND app.SERVICE_ID = :serviceId
        """;
    StringBuilder queryBuilder = new StringBuilder(query);
    Map<String, String> mapColumns =
        Map.of("applicationName", "app.Name", "name", "app.Name", "groupId",
            "config.GROUP_CHAT_ID");
    if (!mapColumns.containsKey(page.getSortBy())) {
      page.setSortBy("applicationName");
    }
    queryBuilder.append(SqlUtils.buildPageQuery(page, mapColumns));

    Map<String, Object> mapParam = new HashMap<>();
    mapParam.put("search", SqlUtils.getSearchEscape(page.getSearch().toLowerCase()));
    mapParam.put("serviceId", serviceId);
    return sqlQueryUtil.queryModel()
        .queryPaging(queryBuilder.toString(), mapParam, TelegramAlertConfigModel.class,
            PageRequest.of(page.getPage(), page.getSize()));
  }
}
