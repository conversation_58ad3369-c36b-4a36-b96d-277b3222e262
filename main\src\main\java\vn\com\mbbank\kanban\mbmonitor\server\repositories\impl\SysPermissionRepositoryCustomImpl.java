package vn.com.mbbank.kanban.mbmonitor.server.repositories.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import vn.com.mbbank.kanban.core.utils.SqlQueryUtil;
import vn.com.mbbank.kanban.mbmonitor.common.entities.SysPermissionEntity;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionActionEnum;
import vn.com.mbbank.kanban.mbmonitor.common.enums.PermissionModuleEnum;
import vn.com.mbbank.kanban.mbmonitor.server.repositories.SysPermissionRepositoryCustom;

/**
 * Information of Author.
 *
 * <AUTHOR>
 * @created_date 10/4/2024
 */
@AllArgsConstructor
public class SysPermissionRepositoryCustomImpl implements SysPermissionRepositoryCustom {
  private final SqlQueryUtil sqlQueryUtil;

  @Override
  public List<SysPermissionEntity> findAllByUserIdAndModuleInAndActionIn(Long userId,
                                                                         List<PermissionModuleEnum> modules,
                                                                         List<PermissionActionEnum> actions) {
    String query = """
                      SELECT
                          sysRolePermission.*
                      FROM
                          SYS_ROLE_PERMISSION sysRolePermission
                      JOIN SYS_USER_ROLE sysUserRole ON
                          sysUserRole.ROLE_ID = sysRolePermission.ROLE_ID
                      JOIN SYS_ROLE sysRole ON sysRole.ID = sysRolePermission.ROLE_ID
                      WHERE
                          sysUserRole.USER_ID = :userId
                          AND sysRole.ACTIVE = '1'
                          AND sysRolePermission.MODULE_NAME IN (:modules)
                          AND sysRolePermission.ACTION IN (:actions)
        """;
    Map<String, Object> map = new HashMap<>();
    map.put("userId", userId);
    map.put("modules", modules.stream().map(Enum::name).toList());
    map.put("actions", actions.stream().map(Enum::name).toList());

    return sqlQueryUtil.queryEntity().querySql(query, map, SysPermissionEntity.class);
  }
}
