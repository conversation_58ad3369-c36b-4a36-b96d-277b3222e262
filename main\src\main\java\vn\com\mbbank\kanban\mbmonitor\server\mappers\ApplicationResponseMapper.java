package vn.com.mbbank.kanban.mbmonitor.server.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import vn.com.mbbank.kanban.core.mapper.KanbanBaseMapper;
import vn.com.mbbank.kanban.mbmonitor.common.entities.ApplicationEntity;
import vn.com.mbbank.kanban.mbmonitor.server.dtos.response.ApplicationResponse;

/**
 * Mapper logic ApplicationResponseMapper.
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ApplicationResponseMapper
    extends KanbanBaseMapper<ApplicationResponse, ApplicationEntity> {
  ApplicationResponseMapper INSTANCE = Mappers.getMapper(ApplicationResponseMapper.class);
}
