package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import org.springframework.stereotype.Repository;
import vn.com.mbbank.kanban.core.repositories.JpaCommonRepository;
import vn.com.mbbank.kanban.mbmonitor.common.entities.MaintenanceTimeConfigEntity;

/**
 * Repository table MaintenanceTimeConfigEntity.
 */
@Repository
public interface MaintenanceTimeConfigRepository
    extends JpaCommonRepository<MaintenanceTimeConfigEntity, Long>, MaintenanceTimeConfigRepositoryCustom {
  /**
   * check maintenance time config existed by id, name and deleted status.
   *
   * @param id   maintenance id
   * @param name maintenance name
   * @return existed or not
   */
  boolean existsByIdNotAndNameIgnoreCase(Long id, String name);

  /**
   * check maintenance time config existed by name.
   *
   * @param name maintenance name
   * @return existed or not
   */
  boolean existsByNameIgnoreCase(String name);


  /**
   * find all maintenance config by active.
   *
   * @param active active status
   * @return next position.
   */
  List<MaintenanceTimeConfigEntity> findAllByActive(Boolean active);

  /**
   * find all maintenance config order by createdDate desc.
   *
   * @return next position.
   */
  List<MaintenanceTimeConfigEntity> findAllByOrderByCreatedDateDesc();
}
