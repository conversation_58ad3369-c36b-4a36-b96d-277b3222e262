package vn.com.mbbank.kanban.mbmonitor.server.repositories;

import java.util.List;
import java.util.Set;
import org.springframework.data.domain.Page;
import vn.com.mbbank.kanban.core.dtos.PaginationRequestDTO;
import vn.com.mbbank.kanban.mbmonitor.common.dtos.responses.VariableResponse;

/**
 * VariableRepositoryCustom.
 */
public interface VariableRepositoryCustom {

  /**
   * Find all.
   *
   * @param page PaginationRequestDTO
   * @return list of VariableResponse
   */
  Page<VariableResponse> findAll(PaginationRequestDTO page);
  
  /**
   * Checks whether there is any duplicate name between VARIABLE and EXECUTION_PARAM
   * for the given executionId.
   *
   * @param executionId the execution ID to filter by
   * @param names the names to filter by
   * @return true if a duplicate name exists; false otherwise
   */
  List<String> findExistsVariableNameInExecution(String executionId, Set<String> names);
  
  /**
   * Returns execution IDs of variables with type 'DYNAMIC_VALUE'
   * that are referenced by parameters in the given execution.
   *
   * @param executionId the execution ID to check
   * @return list of referenced execution IDs
   */
  List<String> getReferencedDynamicExecutionIds(String executionId);
}
